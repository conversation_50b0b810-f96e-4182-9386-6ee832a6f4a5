import { <PERSON>EN<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, STEPS, V3 } from "@/constants";
import duplicateJemTemplate from "@Transform/api/duplicate-jem-template";
import { useCreateExample } from "@v3/examples";
import { useTask } from "@v3/tasks";
import { useCallback } from "react";
import { useNavigate, useParams } from "react-router-dom";

/**
 * This hook can be used to create a new example and navigate to its details page.
 * The currently viewed workflow/task is used to determine the workflow and task ID.
 */
export const useCreateExampleAndNavigate = () => {
  const createExampleMutation = useCreateExample();
  const { workflowId = "", taskId = "", exampleSetId = "" } = useParams();
  const navigate = useNavigate();

  const { data: task } = useTask({
    workflowId,
    taskId,
  });

  const createExampleAndNavigate = useCallback(
    async ({
      copySelectedVersion = true,
      searchParams,
    }: {
      copySelectedVersion: boolean;
      searchParams?: URLSearchParams;
    }) => {
      if (task?.strategy?.kind === "JEM_TEMPLATE_FETCH") {
        await duplicateJemTemplate(taskId, exampleSetId, workflowId);
      }

      createExampleMutation.mutate(
        {
          workflowId,
          taskId,
          example: {
            status: "DRAFT",
            copyFromExampleSetId: copySelectedVersion ? exampleSetId : undefined,
          },
        },
        {
          onSuccess: (newExample) => {
            navigate(
              `/${BUILDER}/${V3}/${AGENTS}/${workflowId}/${STEPS}/${taskId}/${EXAMPLES}/${newExample.id}${searchParams ? `?${searchParams.toString()}` : ""}`,
            );
          },
          onError: (error) => {
            console.error("Error during fetching and mutation", error);
          },
        },
      );
    },
    [createExampleMutation, navigate, taskId, workflowId, exampleSetId, task?.strategy?.kind],
  );

  return {
    createExampleAndNavigate,
    isPending: createExampleMutation.isPending,
  };
};
