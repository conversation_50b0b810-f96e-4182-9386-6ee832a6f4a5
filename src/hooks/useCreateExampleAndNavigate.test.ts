import duplicateJemTemplate from "@Transform/api/duplicate-jem-template";
import { useCreateExample } from "@v3/examples";
import { useTask } from "@v3/tasks";
import { afterEach, describe, expect, test, vi } from "vitest";
import { renderHook } from "vitest-browser-react";
import { useCreateExampleAndNavigate } from "./useCreateExampleAndNavigate";

const mockNavigate = vi.fn();
vi.mock("react-router-dom", () => ({
  useNavigate: () => mockNavigate,
  useParams: () => ({
    workflowId: "test-workflow",
    taskId: "test-task",
    exampleSetId: "test-example-set",
  }),
}));

vi.mock("@v3/tasks");
vi.mock("@v3/examples");
vi.mock("@Transform/api/duplicate-jem-template");

describe("useCreateExampleAndNavigate", () => {
  afterEach(() => {
    vi.resetAllMocks();
  });

  test("should create an example and navigate to its details page", () => {
    // Arrange
    vi.mocked(useTask).mockReturnValue({
      data: {
        strategy: { kind: "SCRIPT" },
      },
    } as unknown as ReturnType<typeof useTask>);

    const createExampleMock = vi.fn((args, { onSuccess }) => {
      // Simulate successful creation of an example
      onSuccess({ id: "new-example-id" });
    });
    vi.mocked(useCreateExample).mockReturnValue({
      mutate: createExampleMock,
    } as unknown as ReturnType<typeof useCreateExample>);

    const { result } = renderHook(() => useCreateExampleAndNavigate());

    // Act
    result.current.createExampleAndNavigate({ copySelectedVersion: true });

    // Assert
    expect(createExampleMock).toHaveBeenCalledWith(
      {
        workflowId: "test-workflow",
        taskId: "test-task",
        example: {
          status: "DRAFT",
          copyFromExampleSetId: "test-example-set",
        },
      },
      expect.anything(),
    );
    expect(mockNavigate).toHaveBeenCalledWith(
      "/builder/v3/agents/test-workflow/steps/test-task/examples/new-example-id",
    );
  });

  test("should call duplicateJemTemplate when task strategy is JEM_TEMPLATE_FETCH", async () => {
    // Arrange
    const duplicateJemTemplateMock = vi.fn();
    vi.mocked(duplicateJemTemplate).mockImplementation(duplicateJemTemplateMock);

    vi.mocked(useTask).mockReturnValue({
      data: {
        strategy: { kind: "JEM_TEMPLATE_FETCH" },
      },
    } as unknown as ReturnType<typeof useTask>);

    const createExampleMock = vi.fn((args, { onSuccess }) => {
      // Simulate successful creation of an example
      onSuccess({ id: "new-example-id" });
    });
    vi.mocked(useCreateExample).mockReturnValue({
      mutate: createExampleMock,
    } as unknown as ReturnType<typeof useCreateExample>);

    const { result } = renderHook(() => useCreateExampleAndNavigate());

    // Act
    await result.current.createExampleAndNavigate({ copySelectedVersion: true });

    // Assert
    expect(duplicateJemTemplateMock).toHaveBeenCalledWith(
      "test-task",
      "test-example-set",
      "test-workflow",
    );
    expect(createExampleMock).toHaveBeenCalled();
    expect(mockNavigate).toHaveBeenCalledWith(
      "/builder/v3/agents/test-workflow/steps/test-task/examples/new-example-id",
    );
  });

  test("should pass searchParams to navigate", () => {
    // Arrange
    vi.mocked(useTask).mockReturnValue({
      data: {
        strategy: { kind: "SCRIPT" },
      },
    } as unknown as ReturnType<typeof useTask>);
    vi.mocked(useCreateExample).mockReturnValue({
      mutate: vi.fn((args, { onSuccess }) => {
        // Simulate successful creation of an example
        onSuccess({ id: "new-example-id" });
      }),
    } as unknown as ReturnType<typeof useCreateExample>);
    const { result } = renderHook(() => useCreateExampleAndNavigate());
    const searchParams = new URLSearchParams({ param1: "value1", param2: "value2" });
    // Act
    result.current.createExampleAndNavigate({ copySelectedVersion: true, searchParams });
    // Assert
    expect(mockNavigate).toHaveBeenCalledWith(
      "/builder/v3/agents/test-workflow/steps/test-task/examples/new-example-id?param1=value1&param2=value2",
    );
  });
});
