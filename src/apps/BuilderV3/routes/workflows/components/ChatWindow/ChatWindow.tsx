import React, { useState, useEffect, useRef } from "react";
import { useMutation, useMutationState, useQuery, useQueryClient } from "@tanstack/react-query";
import { ActionableBadge, <PERSON>ton, Spinner, Checkbox, Tooltip } from "@floqastinc/flow-ui_core";
import {
  Message,
  ExampleInput,
  SetExampleInputDatetimeParams,
  SetExampleInputFileValueParams,
  SetExampleInputNumberParams,
  SetExampleInputTextParams,
} from "@floqastinc/transform-v3";
import { useNavigate, useParams } from "react-router-dom";
import { useAtom } from "jotai";
import { ScopeProvider } from "jotai-scope";
import { WorkflowSourceSchema } from "@floqastinc/transform-v3/lib/v3/schemas";
import { DropEvent, DropzoneRootProps, FileRejection, useDropzone } from "react-dropzone";
import { match, P } from "ts-pattern";
import { debounce } from "lodash";
import { useInputQueries } from "../../BuilderPage.hooks";
import { InputsDropdown } from "../InputsDropdown/InputsDropdown";
import * as Styled from "./styled";
import { ChatMessage, AssistantMessagePending } from "./ChatMessage/ChatMessage";
import {
  DatetimeInput,
  FileInput,
  InputMessage,
  NumberInput,
  TextInput,
} from "./ChatMessage/InputMessage/InputMessage";
import { AddInputActionIcon } from "./AddInputActionIcon";
import { isEditingAtom } from "./ChatMessage/store";
import { InputCheckbox } from "./styled";
import { initialMessage, messagesAtom } from "./store";
import { t } from "@/utils/i18n";
import {
  createMessage,
  getMessagesQuery,
  isConversationBasedStrategy,
} from "@BuilderV3/api/messages";
import { not } from "@/utils/functional";
import { queryKeys } from "@BuilderV3/api/query-keys";
import { getWorkflowTasksQuery } from "@BuilderV3/api/tasks";
import { getTaskInputsQuery } from "@BuilderV3/api/task-inputs";
import { getExampleQuery } from "@BuilderV3/api/examples";
import { getExampleInputsQuery } from "@BuilderV3/api/example-inputs";
import v3 from "@/services/v3";
import { AppSyncService } from "@/services/appsync-service";
import { LocalStorage } from "@/utils/local-storage";
import { useFeatureFlags } from "@/components/FeatureFlag";
import { measureTextWidth } from "@/utils/canvas";
import { BUILDER_PAGE_STRINGS } from "@/utils/string";
import { AGENT } from "@/constants";

const isInputPrompt = (message: Message) => {
  return !!message.metadata?.isInputPrompt;
};
const isMessageActionPrompt = (message: Message) => {
  // @ts-ignore TODO: update this with proper type narrowing
  return !!message.metadata?.isMessageActionPrompt;
};

type ChatWindowProps = {
  selectedRange?: string;
  activeSheet?: string;
};

export const ChatWindow = ({ selectedRange, activeSheet }: ChatWindowProps) => {
  const { workflowId, taskId, exampleSetId } = useParams();
  const navigate = useNavigate();
  if (!workflowId || !taskId || !exampleSetId) {
    console.warn(t("components.ChatWindow.Errors.noIDsprovided"));
    navigate("/builder");
    return null;
  }

  const queryClient = useQueryClient();
  const tasksQuery = useQuery(getWorkflowTasksQuery(workflowId));
  const tasks = tasksQuery.data ?? [];
  const isFirstTask = taskId && tasks && tasks?.[0]?.id === taskId;

  const [messages, setMessages] = useAtom(messagesAtom);
  const [message, setMessage] = useState("");
  const [messageEvents, setMessageEvents] = useState("");
  const messagesEndRef = useRef(null);
  const [includeRange, setIncludeRange] = useState(false);

  const localStorageKey = `chatMessage-${workflowId}-${taskId}-${exampleSetId}`;

  const debouncedSaveToLocalStorage = debounce((message) => {
    LocalStorage.set(localStorageKey, message);
  }, 300);

  useEffect(() => {
    const savedMessage = LocalStorage.get(localStorageKey);
    if (typeof savedMessage === "string") {
      setMessage(savedMessage);
    } else {
      setMessage("");
    }
  }, [localStorageKey]);

  useEffect(() => {
    debouncedSaveToLocalStorage(message);
    return () => debouncedSaveToLocalStorage.cancel();
  }, [message, debouncedSaveToLocalStorage]);

  const sendMessage = useMutation({
    mutationFn: async (message: string) => {
      const taskId_ = taskId || tasks[tasks.length - 1].id;

      let wrappedMsg = message;
      if (includeRange && selectedRange) {
        wrappedMsg = t("components.ChatWindow.theRange", {
          range: selectedRange,
          sheet: activeSheet,
          message,
        });
        setIncludeRange(false);
      }

      return createMessage({
        workflowId,
        taskId: taskId_,
        exampleSetId,
        message: wrappedMsg,
      });
    },
    onError: (err) => {
      console.error(err);
      setMessage("");
    },
  });

  const useIsMutationPending = (
    mutationKey: (string | { workflowId: string; taskId: string; exampleSetId: string })[],
  ) => {
    return useMutationState({
      filters: { mutationKey },
      select: (mutation) => mutation.state.status,
    }).some((status) => status === "pending");
  };

  const editIsPending = useIsMutationPending(["editMessage", { workflowId, taskId, exampleSetId }]);

  const deletionIsPending = useIsMutationPending([
    "deleteMessage",
    { workflowId, taskId, exampleSetId },
  ]);

  const regenerationIsPending = useIsMutationPending([
    "regenerateMessage",
    { workflowId, taskId, exampleSetId },
  ]);

  const exampleSetQuery = useQuery({
    ...getExampleQuery({
      workflowId,
      taskId,
      exampleSetId,
    }),
    refetchInterval: (query) => {
      // If no message mutations are pending and the thread run is busy, then refetch every 1.5 seconds
      const isMessageMutationPending =
        sendMessage.isPending || editIsPending || deletionIsPending || regenerationIsPending;
      const isThreadRunBusy =
        query.state.data &&
        isConversationBasedStrategy(query.state.data.strategy) &&
        query.state.data.strategy.conversationStatus === "BUSY";
      if (!isMessageMutationPending && isThreadRunBusy) {
        return 1500;
      }
      return undefined;
    },
  });

  const isDraft = exampleSetQuery.data?.status === "DRAFT";

  const messagesQuery = useQuery({
    ...getMessagesQuery({ workflowId, taskId, exampleSetId }),
    enabled: !!exampleSetQuery.data,
    select: (messages) => {
      // Regex to match variations of "Download the <file> file" (e.g., "Download the highlighted file", etc.)
      const fileDownloadRegex =
        /You can download the (highlighted|modified|updated|filtered|correct|latest|.*) file(?: using the link below:)?/gi;

      // Regex to match the file link pattern (sandbox path or URLs)
      const fileLinkRegex = /\[.*\]\(sandbox:[^\)]+\)/gi;

      return messages
        .filter(not(isInputPrompt))
        .filter(not(isMessageActionPrompt))
        .map((message) => ({
          id: message.id,
          content: message.content
            .replace(fileDownloadRegex, BUILDER_PAGE_STRINGS.USE_DOWNLOAD_BUTTON)
            .replace(fileLinkRegex, "")
            .trim(),
          role: message.role ? message.role : "assistant",
        }))
        .reverse();
    },
  });

  const exampleInputsQuery = useQuery(getExampleInputsQuery({ workflowId, taskId, exampleSetId }));

  const taskInputsQuery = useQuery(getTaskInputsQuery({ workflowId, taskId }));
  /** Inputs for example with metadata from the task input data */
  // @ts-ignore - TypeScript is not properly catching the filter(Boolean)
  const mergedExampleInputs = exampleInputsQuery.data
    ?.map((inputFile) => {
      const taskInputFile = taskInputsQuery.data?.find(
        (taskInputFile) => taskInputFile.id === inputFile.taskInputId,
      );
      if (!taskInputFile) return null;
      const validationResult = WorkflowSourceSchema.safeParse(taskInputFile.source);
      return {
        ...inputFile,
        meta: {
          name: taskInputFile.name,
          workflowInputId: validationResult.success
            ? taskInputFile.source.workflowInputId
            : undefined,
        },
      };
    })
    .filter(Boolean);

  useEffect(() => {
    if (messagesQuery.isSuccess) {
      if (messagesQuery.data?.length > 1) {
        setMessages([
          ...messagesQuery.data.filter(not(isInputPrompt)).map((message) => {
            return {
              content: message.content,
              role: message.role ? message.role : "assistant",
              id: message.id,
            };
          }),
        ]);
      } else {
        setMessages([initialMessage]);
      }
    }
  }, [messagesQuery.isSuccess, messagesQuery.data, mergedExampleInputs?.length]);

  const { addInputMutation } = useInputQueries();

  const scrollToBottomOfMessages = () => {
    // @ts-ignore
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    // TODO this won't work on page load as we're loading the messages async, probably need a way to figure out when we've done the initial load that doesn't refire on subsequent loads
    scrollToBottomOfMessages();
  }, []); // scroll to bottom on page load

  // Assumes first message is the base assistant message
  const hasUserMessages =
    messages.length > 1 && messages.some((message) => message.role === "user");

  const dontAllowSend =
    !tasks.length || !message || sendMessage.isPending || addInputMutation.isPending;

  const sendButtonClicked = () => {
    sendMessage.mutate(message);
    setMessages([...messages, { id: undefined, content: message, role: "user" }]);
    setMessage("");
    // this is a hack but quicker than trying to differentiate when messages are created whether they're
    // from the user or the LLM
    setTimeout(scrollToBottomOfMessages, 0);
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if ((event.metaKey || event.ctrlKey) && event.key === "Enter") {
      if (!dontAllowSend) {
        sendButtonClicked();
      }
    }
  };

  const addInputToTaskIsPending = useMutationState({
    filters: {
      mutationKey: ["addInputToTask", { workflowId, taskId }],
    },
    select: (mutation) => mutation.state.status,
  }).some((status) => status === "pending");
  const inputEditIsPending = useMutationState({
    filters: {
      mutationKey: ["setExampleInput", { workflowId }],
    },
    select: (mutation) => mutation.state.status,
  }).some((status) => status === "pending");

  const shouldRenderSpinner =
    (tasks.length && messagesQuery.isPending && messagesQuery.fetchStatus !== "idle") ||
    deletionIsPending ||
    addInputToTaskIsPending ||
    inputEditIsPending;

  const isThreadRunBusy =
    exampleSetQuery.data && isConversationBasedStrategy(exampleSetQuery.data.strategy)
      ? exampleSetQuery.data.strategy.conversationStatus === "BUSY"
      : false;
  const isThreadRunFailed =
    exampleSetQuery.data && isConversationBasedStrategy(exampleSetQuery.data.strategy)
      ? exampleSetQuery.data.strategy.conversationStatus === "FAILED"
      : false;
  const assistantMessageIsPending =
    sendMessage.isPending || editIsPending || regenerationIsPending || isThreadRunBusy;

  useEffect(() => {
    // If any message action goes from pending to not pending, then refetch messages and outputs
    // Include check for messagesQuery.isPending to prevent firing on initial load
    if (
      !assistantMessageIsPending &&
      // this is specifically excluded from assistantMessageIsPending
      !deletionIsPending &&
      !messagesQuery.isPending
    ) {
      queryClient.invalidateQueries({
        queryKey: queryKeys.messages.getMessages({
          workflowId,
          taskId,
          exampleSetId,
        }),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.taskOutputs.byTask({ workflowId, taskId }),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.exampleOutputs.getExampleOutputs({
          workflowId,
          taskId,
          exampleSetId,
        }),
      });
      queryClient.invalidateQueries({
        queryKey: [
          {
            resource: "exampleOutputs",
            params: { workflowId, taskId, exampleSetId },
          },
        ],
      });
    }
  }, [assistantMessageIsPending, deletionIsPending]);

  // Handle real time messages
  useEffect(() => {
    let appsyncService: AppSyncService<string> | null = null;
    // Keep track of the disconnect function
    let disconnect: () => void = () => {};

    // Opening a connection to the server to begin receiving events
    const handleConnection = async () => {
      try {
        const handleMessageData = (event: any): void => {
          try {
            setMessageEvents(event);
          } catch (error) {
            console.error(error);
          }
        };

        if (!(workflowId && taskId && exampleSetId)) {
          throw new Error(
            JSON.stringify({
              event: "handle connecting to appsync client",
              details: {
                message: t("components.ChatWindow.Errors.appSyncAuthenticationValidationInvalid", {
                  workflowId,
                  taskId,
                  exampleSetId,
                }),
                workflowId,
                taskId,
                exampleSetId,
              },
            }),
          );
        }

        appsyncService = new AppSyncService<string>({
          workflowId,
          taskId,
          exampleSetId,
        });
        disconnect = await appsyncService.connect({ handleMessageData });
      } catch (error) {
        console.error(error);
      }
    };

    // Include check for messagesQuery.isPending to prevent firing on initial load
    if (messagesQuery.isPending) {
      return;
    }

    // If any message action goes from pending to not pending, then disconnect
    // from AppSync

    if (
      !assistantMessageIsPending &&
      // this is specifically excluded from assistantMessageIsPending
      !deletionIsPending
    ) {
      disconnect();
      appsyncService = null;
      // if realtime-messages is undefined or true, enable real-time messages
    } else if (getFlag("realtime-messages", false) !== false) {
      setMessageEvents("");
      handleConnection();
    }

    // Cleanup function to disconnect
    return () => disconnect();
  }, [assistantMessageIsPending, deletionIsPending]);

  async function dropHandler(
    acceptedFiles: File[],
    rejectedFiles: FileRejection[],
    _ev: DropEvent,
  ) {
    if (acceptedFiles.length) {
      await Promise.all(
        acceptedFiles.map((file) =>
          addInputMutation.mutate({
            description: file.name,
            name: file.name,
            type: "FILE",
            value: file,
          }),
        ),
      );
    }
    // TODO: handle rejected files
    if (rejectedFiles.length) {
      console.error(t("components.ChatWindow.Errors.rejectedFiles"), rejectedFiles);
    }
  }

  const { getRootProps, isDragActive } = useDropzone({
    onDrop: dropHandler,
    accept: {
      "text/csv": [".csv"],
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx"],
    },
    disabled: !isDraft,
  });

  const rootProps: DropzoneRootProps = getRootProps({
    $isDragActive: isDragActive,
  });
  const { onDragEnter, onDragLeave, onDrop, ref, $isDragActive } = rootProps;
  const filteredProps = {
    onDragEnter,
    onDragLeave,
    onDrop,
    ref,
    $isDragActive,
  };

  type SetExampleInputValueParams =
    | SetExampleInputFileValueParams
    | SetExampleInputTextParams
    | SetExampleInputNumberParams
    | SetExampleInputDatetimeParams;
  const setValue = useMutation({
    mutationKey: ["setExampleInputValue", { workflowId, taskId, exampleSetId }],
    mutationFn: async ({
      input,
      newValue,
    }: {
      input: SetExampleInputValueParams;
      newValue: any;
    }) => {
      const fn = match([input.value, newValue])
        .with([{ kind: "FILE" }, P.any], (_value) => null)
        .with([{ workflowInputId: P.any }, P.any], (_value) => null)
        .with([{ kind: "TEXT" }, P.any], () =>
          v3.exampleInputs.setExampleInputText({
            ...input,
            value: { kind: "TEXT", value: newValue },
          }),
        )
        .with([{ kind: "NUMBER" }, P.any], () =>
          v3.exampleInputs.setExampleInputNumber({
            ...input,
            value: { kind: "NUMBER", value: newValue },
          }),
        )
        .with([{ kind: "DATETIME" }, P.any], () =>
          v3.exampleInputs.setExampleInputDatetime({
            ...input,
            value: { kind: "DATETIME", value: newValue },
          }),
        )
        .exhaustive();
      return fn;
    },
    onSettled: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.exampleInputs.getExampleInputs({
          workflowId,
          taskId,
          exampleSetId,
        }),
      });
    },
  });

  const userFriendlyInputType = (kind: "FILE" | "TEXT" | "NUMBER" | "DATETIME") =>
    match(kind)
      .with("FILE", () => "File")
      .with("TEXT", () => "Text")
      .with("NUMBER", () => "Number")
      .with("DATETIME", () => "Datetime")
      .exhaustive();

  const { getFlag } = useFeatureFlags();

  // TODO: this should be temporary. when a user drag/drops a file,
  // it automatically creates an input with the file name as the input name.
  // we should either update the dropzone to render the input modal or allow for editing input names.
  const hasUniqueInputName = (
    input: ExampleInput & { meta: { name: string; workflowInputId?: string } },
  ) => {
    if (input.value && "name" in input.value && input.meta.name === input.value.name) {
      return false;
    }
    return true;
  };

  const getInputMessages = () => {
    return mergedExampleInputs?.map((input, i) => {
      return input.value ? (
        <InputMessage includeAvatar={i === 0} key={input.id}>
          <ActionableBadge hideX style={{ cursor: "default" }} onClick={() => {}}>
            {userFriendlyInputType(input.value.kind)}
          </ActionableBadge>
          {hasUniqueInputName(input) && <p style={{ fontWeight: "bold" }}>{input.meta.name}</p>}
          {match(input)
            .with({ value: { kind: "FILE" } }, (input) =>
              // tooltip does not have disabled prop so conditional is necessary
              measureTextWidth(input.value.name) >= 175 ? (
                <Tooltip>
                  <Tooltip.Trigger>
                    <FileInput input={input} />
                  </Tooltip.Trigger>
                  <Tooltip.Content>{input.value.name}</Tooltip.Content>
                </Tooltip>
              ) : (
                <FileInput input={input} />
              ),
            )
            .with({ value: { kind: "TEXT" } }, (input) => (
              <TextInput
                key={input.id}
                value={input.value.value}
                disabled={!isDraft}
                onSave={(val) =>
                  setValue.mutate({
                    input: { ...input, exampleInputId: input.id },
                    newValue: val,
                  })
                }
              />
            ))
            .with({ value: { kind: "NUMBER" } }, (input) => (
              <NumberInput
                key={input.id}
                value={input.value.value}
                disabled={!isDraft}
                onSave={(val) =>
                  setValue.mutate({
                    input: { ...input, exampleInputId: input.id },
                    newValue: val,
                  })
                }
              />
            ))
            .with({ value: { kind: "DATETIME" } }, (input) => (
              <DatetimeInput
                key={input.id}
                value={input.value.value}
                disabled={!isDraft}
                onChange={(val) =>
                  setValue.mutate({
                    input: { ...input, exampleInputId: input.id },
                    newValue: val,
                  })
                }
              />
            ))
            // @ts-ignore - TypeScript is not properly recognizing input.value condition
            .exhaustive()}
        </InputMessage>
      ) : null;
    });
  };

  return (
    <Styled.ChatWindow {...filteredProps}>
      {shouldRenderSpinner ? (
        <Styled.Centered>
          <Spinner color="success" size={48} />
        </Styled.Centered>
      ) : null}
      <Styled.Messages $leaveSpaceForChatBox={isDraft}>
        {isDraft && !isFirstTask ? <InputsDropdown /> : null}
        {getInputMessages()}
        {messages.map((message, i) => {
          return (
            <ScopeProvider atoms={[isEditingAtom]} key={message.id ?? i}>
              <ChatMessage
                message={message}
                assistantMessageIsPending={assistantMessageIsPending}
              />
            </ScopeProvider>
          );
        })}
        {assistantMessageIsPending && messageEvents ? (
          <div>
            <ChatMessage
              message={{
                id: undefined,
                role: "assistant",
                content: messageEvents,
              }}
              assistantMessageIsPending={false}
            />
          </div>
        ) : null}
        {assistantMessageIsPending ? <AssistantMessagePending /> : null}
        {!assistantMessageIsPending && (sendMessage.isError || isThreadRunFailed) ? (
          <ChatMessage
            message={{
              id: undefined,
              role: "assistant",
              content: t("components.ChatWindow.Errors.unexpectedInternalError"),
            }}
            assistantMessageIsPending={assistantMessageIsPending}
          />
        ) : null}
        <span className={"lastSpan"} ref={messagesEndRef}></span>
      </Styled.Messages>
      {/* TODO: switch over to FlowUI TextArea */}
      {isDraft ? (
        <Styled.ChatBox>
          <Styled.TextArea
            placeholder={t("components.ChatWindow.chatWithAI", {
              agent: AGENT.toLocaleLowerCase(),
            })}
            rows={8}
            value={message}
            onChange={(e) => {
              setMessage(e.target.value);
            }}
            onKeyDown={handleKeyDown}
          />
          <Styled.TextAreaActions>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                marginLeft: "10px",
              }}
            >
              {taskId ? (
                <AddInputActionIcon disabled={!tasks.length} addInput={addInputMutation.mutate} />
              ) : null}
            </div>
            <Styled.TextAreaActionsRight>
              <InputCheckbox>
                <Tooltip>
                  <Tooltip.Trigger>
                    <Checkbox
                      disabled={!tasks.length}
                      checked={includeRange}
                      onClick={() => setIncludeRange(!includeRange)}
                      data-tracking-id="builder-include-range-checkbox"
                    />
                  </Tooltip.Trigger>
                  <Tooltip.Content hasArrow>
                    {t("components.ChatWindow.includeSelectedRange")}
                  </Tooltip.Content>
                </Tooltip>
              </InputCheckbox>
              <Button
                disabled={dontAllowSend}
                onClick={sendButtonClicked}
                data-tracking-id="builder-send-chat-message-button"
              >
                {assistantMessageIsPending ? <Spinner /> : t("components.Chat.send")}
              </Button>
            </Styled.TextAreaActionsRight>
          </Styled.TextAreaActions>
        </Styled.ChatBox>
      ) : null}
    </Styled.ChatWindow>
  );
};
