/* eslint-disable no-restricted-syntax */
/* eslint-disable no-useless-escape */
import { FlolakeConnectionData } from "@/api/shared/types";

export const createSystemKey = (system: FlolakeConnectionData): string => {
  return `${system.transformSystem} - ${system.connectionName}`;
};

export const convertTokenToSchema = (
  query: string,
  flolakeData: FlolakeConnectionData[],
): string => {
  if (!query) return "";

  // TODO: We should format this data upon fetching connections
  const tableNameToSchema = new Map<string, string>();
  const systemKeyToSystem = new Map<string, FlolakeConnectionData>();
  const schemaToToken = new Map<string, string>();

  for (const system of flolakeData) {
    const systemKey = createSystemKey(system);
    systemKeyToSystem.set(systemKey.toLowerCase(), system);

    if (system.tableData) {
      for (const table of system.tableData) {
        tableNameToSchema.set(table.tableName.toLowerCase(), table.schemaName.toLowerCase());
        // Add direct schema to token mapping for convertSchemaToToken
        schemaToToken.set(table.schemaName.toLowerCase(), systemKey);
        schemaToToken.set(system.schemaName.toLowerCase(), systemKey);
      }
    }
  }

  return query.replace(/\{([^}]+)\}/g, (match, tokenContent) => {
    // Skip variables (they start with $)
    if (tokenContent.startsWith("$")) {
      return match; // Leave variables unchanged for later processing
    }

    const tokenLower = tokenContent.toLowerCase();

    // First, check if this is a table token
    if (tableNameToSchema.has(tokenLower)) {
      // This is a table token - return just the table name
      return tokenContent; // Keep original case
    }

    // Check if this is a system token
    const system = systemKeyToSystem.get(tokenLower);
    if (system) {
      // Look for any table tokens in the query that belong to this system
      const tableTokens = query.match(/\{([^}]+)\}/g) || [];

      for (const tableToken of tableTokens) {
        const tableName = tableToken.slice(1, -1).toLowerCase();

        // Check if this table belongs to the current system
        const tableInSystem = system.tableData?.find(
          (table) => table.tableName.toLowerCase() === tableName,
        );

        if (tableInSystem) {
          // Use this table's schema name for the system token
          return tableInSystem.schemaName.toLowerCase();
        }
      }

      // Fallback to system's schema name
      return system.schemaName;
    }

    // Otherwise, just remove curly braces for table/column
    return tokenContent;
  });
};

export const convertVariablesToSnowflake = (
  query: string,
  taskInputs: Array<{ id: string; name: string; type: string }>,
  variableIdMapping?: Record<string, string>,
): { convertedQuery: string; bindMapping: string[] } => {
  if (!query) return { convertedQuery: "", bindMapping: [] };

  const bindMapping: string[] = [];
  const variableRegex = /\{\$([^}]+)\}/g;
  let parameterIndex = 1;

  // Track how many times we've seen each base variable name (without suffix)
  const variableNameCounts: Record<string, number> = {};

  const convertedQuery = query.replace(variableRegex, (match, variableName) => {
    // Check if this is a numbered variable (e.g., "Amount_2")
    const numberedMatch = variableName.match(/^(.+)_(\d+)$/);
    const baseVariableName = numberedMatch ? numberedMatch[1] : variableName;
    const explicitIndex = numberedMatch ? parseInt(numberedMatch[2], 10) : null;

    // Increment the count for the base variable name
    variableNameCounts[baseVariableName] = (variableNameCounts[baseVariableName] || 0) + 1;
    const occurrenceIndex = variableNameCounts[baseVariableName];

    let taskInput;

    // First try to use the variable ID mapping if provided (using original variable name)
    if (variableIdMapping && variableIdMapping[variableName]) {
      taskInput = taskInputs.find((input) => input.id === variableIdMapping[variableName]);
    }

    // If mapping didn't work or wasn't provided, find by name
    if (!taskInput) {
      // Find all inputs with the base name
      const matchingInputs = taskInputs.filter((input) => input.name === baseVariableName);

      if (matchingInputs.length === 1) {
        // Only one input with this name, use it
        taskInput = matchingInputs[0];
      } else if (matchingInputs.length > 1) {
        // Multiple inputs with same name
        let inputIndex;

        if (explicitIndex !== null) {
          // Use the explicit index from numbered variable (e.g., Amount_2 → index 2)
          inputIndex = Math.min(explicitIndex - 1, matchingInputs.length - 1);
        } else {
          // Use the occurrence index for non-numbered variables
          inputIndex = Math.min(occurrenceIndex - 1, matchingInputs.length - 1);
        }

        taskInput = matchingInputs[inputIndex];

        console.warn(
          `Multiple inputs found with name "${baseVariableName}". Using ${explicitIndex ? `explicit index ${explicitIndex}` : `occurrence ${occurrenceIndex}`} → input ID: ${taskInput.id}`,
        );
      }

      // Warn if we had to fall back to name matching when mapping was provided
      if (taskInput && variableIdMapping) {
        console.warn(
          `Variable "${variableName}" not found in mapping, fell back to name matching with ID: ${taskInput.id}`,
        );
      }
    }

    if (taskInput) {
      bindMapping.push(taskInput.id);
      return `:${parameterIndex++}`;
    }

    // If no matching task input found, leave as is for now
    console.warn(`No matching task input found for variable: ${variableName}`);
    return match;
  });

  return { convertedQuery, bindMapping };
};

export const convertSnowflakeToVariables = (
  query: string,
  bindMapping: string[],
  taskInputs: Array<{ id: string; name: string; type: string }>,
): string => {
  if (!query || !bindMapping.length) return query;

  let convertedQuery = query;

  // First, group inputs by name to identify duplicates
  const inputsByName: Record<string, Array<{ id: string; name: string; type: string }>> = {};
  taskInputs.forEach((input) => {
    if (!inputsByName[input.name]) {
      inputsByName[input.name] = [];
    }
    inputsByName[input.name].push(input);
  });

  // Replace :1, :2, etc. with {$variableName}
  bindMapping.forEach((taskInputId, index) => {
    const taskInput = taskInputs.find((input) => input.id === taskInputId);
    if (taskInput) {
      const parameterPattern = new RegExp(`:${index + 1}\\b`, "g");

      // Check if there are multiple inputs with the same name
      const inputsWithSameName = inputsByName[taskInput.name];
      let variableName = taskInput.name;

      if (inputsWithSameName && inputsWithSameName.length > 1) {
        // Find the index of this specific input among those with the same name
        const inputIndex = inputsWithSameName.findIndex((input) => input.id === taskInputId);
        if (inputIndex >= 0) {
          variableName = `${taskInput.name}_${inputIndex + 1}`;
        }
      }

      convertedQuery = convertedQuery.replace(parameterPattern, `{$${variableName}}`);
    } else {
      console.warn(`    No task input found for ID: ${taskInputId}`);
    }
  });

  return convertedQuery;
};

export const convertSchemaToToken = (
  query: string,
  flolakeData: FlolakeConnectionData[],
): string => {
  if (!query) return "";
  // TODO: We should format this data upon fetching connections
  // Create the schema to token mapping
  const schemaToToken = new Map<string, string>();

  for (const system of flolakeData) {
    const systemKey = createSystemKey(system);

    if (system.tableData) {
      for (const table of system.tableData) {
        // Map both table's schema name and system's schema name to tokens
        schemaToToken.set(table.schemaName.toLowerCase(), systemKey);
        schemaToToken.set(system.schemaName.toLowerCase(), systemKey);
      }
    }
  }

  // Sort by schema name length descending to avoid partial matches
  const sortedSchemas = Array.from(schemaToToken.keys()).sort((a, b) => b.length - a.length);

  if (sortedSchemas.length === 0) return query;

  // Build a regex that matches any schema name
  const schemaPattern = sortedSchemas
    .map((schema) => schema.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&"))
    .join("|");

  // Regex: match schema[.table][.column]
  const regex = new RegExp(
    `\\b(${schemaPattern})(?:\\.([A-Za-z0-9_]+))?(?:\\.([A-Za-z0-9_]+))?\\b`,
    "gi",
  );

  return query.replace(regex, (match, schema, table, column) => {
    const schemaLower = schema.toLowerCase();
    const systemKey = schemaToToken.get(schemaLower);

    if (!systemKey) return match; // Shouldn't happen, but fallback

    let result = `{${systemKey}}`;
    if (table) {
      // If we have a table name, use it instead of the system key for the table part
      result += `.{${table.toUpperCase()}}`;
    }
    if (column) {
      result += `.{${column.toUpperCase()}}`;
    }
    return result;
  });
};
