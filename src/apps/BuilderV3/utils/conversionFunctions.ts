import { FlolakeConnectionData } from "@/api/shared/types";

export const createSystemKey = (system: FlolakeConnectionData): string => {
  return `${system.transformSystem} - ${system.connectionName}`;
};

export const convertTokenToSchema = (
  query: string,
  flolakeData: FlolakeConnectionData[],
): string => {
  if (!query) return "";

  return query.replace(/\{([^}]+)\}/g, (match, tokenContent) => {
    // Skip variables (they start with $)
    if (tokenContent.startsWith("$")) {
      return match; // Leave variables unchanged for later processing
    }

    // Try to match system+connection
    const sysMatch = flolakeData.find((sys) => {
      const systemKey = createSystemKey(sys);
      return systemKey === tokenContent || systemKey.toLowerCase() === tokenContent.toLowerCase();
    });

    if (sysMatch) {
      return sysMatch.schemaName;
    }
    // Otherwise, just remove curly braces for table/column
    return tokenContent;
  });
};

export const convertVariablesToSnowflake = (
  query: string,
  taskInputs: Array<{ id: string; name: string; type: string }>,
  variableIdMapping?: Record<string, string>,
): { convertedQuery: string; bindMapping: string[] } => {
  if (!query) return { convertedQuery: "", bindMapping: [] };

  const bindMapping: string[] = [];
  const variableRegex = /\{\$([^}]+)\}/g;
  let parameterIndex = 1;

  // Track how many times we've seen each base variable name (without suffix)
  const variableNameCounts: Record<string, number> = {};

  const convertedQuery = query.replace(variableRegex, (match, variableName) => {
    // Check if this is a numbered variable (e.g., "Amount_2")
    const numberedMatch = variableName.match(/^(.+)_(\d+)$/);
    const baseVariableName = numberedMatch ? numberedMatch[1] : variableName;
    const explicitIndex = numberedMatch ? parseInt(numberedMatch[2], 10) : null;

    // Increment the count for the base variable name
    variableNameCounts[baseVariableName] = (variableNameCounts[baseVariableName] || 0) + 1;
    const occurrenceIndex = variableNameCounts[baseVariableName];

    let taskInput;

    // First try to use the variable ID mapping if provided (using original variable name)
    if (variableIdMapping && variableIdMapping[variableName]) {
      taskInput = taskInputs.find((input) => input.id === variableIdMapping[variableName]);
    }

    // If mapping didn't work or wasn't provided, find by name
    if (!taskInput) {
      // Find all inputs with the base name
      const matchingInputs = taskInputs.filter((input) => input.name === baseVariableName);

      if (matchingInputs.length === 1) {
        // Only one input with this name, use it
        taskInput = matchingInputs[0];
      } else if (matchingInputs.length > 1) {
        // Multiple inputs with same name
        let inputIndex;

        if (explicitIndex !== null) {
          // Use the explicit index from numbered variable (e.g., Amount_2 → index 2)
          inputIndex = Math.min(explicitIndex - 1, matchingInputs.length - 1);
        } else {
          // Use the occurrence index for non-numbered variables
          inputIndex = Math.min(occurrenceIndex - 1, matchingInputs.length - 1);
        }

        taskInput = matchingInputs[inputIndex];

        console.warn(
          `Multiple inputs found with name "${baseVariableName}". Using ${explicitIndex ? `explicit index ${explicitIndex}` : `occurrence ${occurrenceIndex}`} → input ID: ${taskInput.id}`,
        );
      }

      // Warn if we had to fall back to name matching when mapping was provided
      if (taskInput && variableIdMapping) {
        console.warn(
          `Variable "${variableName}" not found in mapping, fell back to name matching with ID: ${taskInput.id}`,
        );
      }
    }

    if (taskInput) {
      bindMapping.push(taskInput.id);
      return `:${parameterIndex++}`;
    }

    // If no matching task input found, leave as is for now
    console.warn(`No matching task input found for variable: ${variableName}`);
    return match;
  });

  return { convertedQuery, bindMapping };
};

export const convertSnowflakeToVariables = (
  query: string,
  bindMapping: string[],
  taskInputs: Array<{ id: string; name: string; type: string }>,
): string => {
  if (!query || !bindMapping.length) return query;

  let convertedQuery = query;

  // First, group inputs by name to identify duplicates
  const inputsByName: Record<string, Array<{ id: string; name: string; type: string }>> = {};
  taskInputs.forEach((input) => {
    if (!inputsByName[input.name]) {
      inputsByName[input.name] = [];
    }
    inputsByName[input.name].push(input);
  });

  // Replace :1, :2, etc. with {$variableName}
  bindMapping.forEach((taskInputId, index) => {
    const taskInput = taskInputs.find((input) => input.id === taskInputId);
    if (taskInput) {
      const parameterPattern = new RegExp(`:${index + 1}\\b`, "g");

      // Check if there are multiple inputs with the same name
      const inputsWithSameName = inputsByName[taskInput.name];
      let variableName = taskInput.name;

      if (inputsWithSameName && inputsWithSameName.length > 1) {
        // Find the index of this specific input among those with the same name
        const inputIndex = inputsWithSameName.findIndex((input) => input.id === taskInputId);
        if (inputIndex >= 0) {
          variableName = `${taskInput.name}_${inputIndex + 1}`;
        }
      }

      convertedQuery = convertedQuery.replace(parameterPattern, `{$${variableName}}`);
    } else {
      console.warn(`    No task input found for ID: ${taskInputId}`);
    }
  });

  return convertedQuery;
};

export const convertSchemaToToken = (
  query: string,
  flolakeData: FlolakeConnectionData[],
): string => {
  if (!query) return "";

  // Sort by schemaName length descending to avoid partial matches
  const sortedData = [...flolakeData].sort((a, b) => b.schemaName.length - a.schemaName.length);

  // Build a regex that matches any system at the start of a reference
  const systemNames = sortedData.map((sys) =>
    sys.schemaName.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&"),
  );
  if (systemNames.length === 0) return query;
  const systemPattern = systemNames.join("|");

  // Regex: match system[.table][.column], but only if it starts with a system
  // e.g. NETSUITE.ACCOUNT._FIVETRAN_DELETED or NETSUITE.ACCOUNT or NETSUITE
  const regex = new RegExp(
    `\\b(${systemPattern})(?:\\.([A-Za-z0-9_]+))?(?:\\.([A-Za-z0-9_]+))?\\b`,
    "gi",
  );

  return query.replace(regex, (match, sys, table, column) => {
    // Find the system in flolakeData (case-insensitive)
    const systemObj = sortedData.find((s) => s.schemaName.toLowerCase() === sys.toLowerCase());
    if (!systemObj) return match; // Shouldn't happen, but fallback

    const systemKey = createSystemKey(systemObj);
    let result = `{${systemKey}}`;
    if (table) result += `.{${table.toUpperCase()}}`;
    if (column) result += `.{${column.toUpperCase()}}`;
    return result;
  });
};
