import { Suspense, useEffect, useState, useCallback, useMemo } from "react";
import { useMatch, useParams } from "react-router-dom";
import { match } from "ts-pattern";
import { Spinner } from "@floqastinc/flow-ui_core";
import { useQuery } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { EditorView } from "codemirror";
import { FilePreview } from "../runner/ui/FilePreview";
import * as Styled from "./index.styles";
import { Header } from "./ui/Header";
import { Chat } from "./ui/Chat";
import { NewTaskPopover } from "./ui/NewTaskPopover";
import { BuilderSectionHeader } from "./ui/BuilderSectionHeader";
import { BuilderSectionTab, BuilderSectionTabs } from "./ui/BuilderSectionTabs";
import { InputsTabSection } from "./ui/BuilderSectionTabs/InputsTabSection";
import { BuilderSectionActionBar } from "./ui/BuilderSectionActionBar";
import { StepsSidebarList } from "./ui/StepsSidebarList";
import { fileAtom, isFileLoadingAtom } from "./store";
import { VersionsTab } from "./ui/BuilderSectionTabs/VersionsTab/VersionsTab";
import { DataSourcesTree } from "./ui/DataSourcesTree/DataSourcesTree";
import { DetailsTab } from "./ui/BuilderSectionTabs/DetailsTab";
import GlobalInputs from "./ui/GlobalInputs";
import { t } from "@/utils/i18n";
import { useRedirectToActiveExampleSet } from "@/hooks/useRedirectToActiveExampleSet";
import { useTasks } from "@v3/tasks";
import { SQLEditorSplit } from "@Transform/pages/builder/ui/SQLEditorSplit/SQLEditorSplit";
import { useFilePreviewForCurrentTask } from "@/hooks/useFilePreviewForCurrentTask";
import { getExampleQuery } from "@BuilderV3/api/examples";
import { useFlolakeData } from "@/api/shared/connections-query";
import { BUILDER, INPUTS_PATH, V3 } from "@/constants";

type OpenedFile = {
  id: string;
  name: string;
  type: "Input" | "Output" | "PreviousOutput";
  stepNumber?: number;
  taskInputId?: string;
  taskOutputId?: string;
  displayName: string;
};
export const BuilderPage = () => {
  const onInputsPage = useMatch(`/${BUILDER}/${V3}/${INPUTS_PATH}`);

  const { workflowId = "", taskId = "", exampleSetId = "" } = useParams();
  const [, setFile] = useAtom(fileAtom);
  const [, setIsFileLoading] = useAtom(isFileLoadingAtom);

  const { isRedirecting } = useRedirectToActiveExampleSet({ disabled: !!onInputsPage });

  const tasksQuery = useTasks(
    {
      workflowId,
    },
    {
      enabled: !!workflowId,
    },
  );
  const currentTask = tasksQuery.data?.find((task) => task.id === taskId);
  const taskStrategy = currentTask?.strategy;

  const {
    file: file_,
    files,
    setSelectedFile,
    isLoading: isFileLoading,
    hasCompletedInitialLoad,
  } = useFilePreviewForCurrentTask({ workflowId, taskId, exampleSetId });

  const isFlolakeStrategy = taskStrategy?.kind === "FLOLAKE";
  const stepNumber = tasksQuery.data?.findIndex((task) => task.id === taskId);
  const filePreviewStepNumberText =
    (stepNumber === 0 || stepNumber) && stepNumber !== -1
      ? t("components.Builder.stepOutput", { index: stepNumber + 1 })
      : "";

  const [openedFiles, setOpenedFiles] = useState<OpenedFile[]>([]);
  const [activeFileId, setActiveFileId] = useState<string | null>(null);
  const [file, setFileState] = useState<File | null>(null);
  const [hasInitializedFiles, setHasInitializedFiles] = useState(false);

  useEffect(() => {
    setFileState(file_);
  }, [file_]);

  // Reset file state when workflow changes
  useEffect(() => {
    setFile(null);
    setIsFileLoading(false);
    setOpenedFiles([]);
    setActiveFileId(null);
    setFileState(null);
    setHasInitializedFiles(false);
  }, [workflowId, taskId]);

  // Reset tabs when task changes
  useEffect(() => {
    setTab("build");
  }, [taskId]);

  // Update opened files (FileTabs) to the new example set files
  useEffect(() => {
    if (!hasCompletedInitialLoad) return;
    if (!files || !openedFiles.length) return;

    const newOpenedFiles = openedFiles
      .map((openedFile) => {
        if (openedFile.type === "Input") {
          return files.inputs?.find((f) => f.taskInputId === openedFile.taskInputId) ?? null;
        }
        if (openedFile.type === "PreviousOutput") {
          return files.previousOutputs?.find((f) => f.stepNumber === openedFile.stepNumber) ?? null;
        }
        return null;
      })
      .filter((file) => !!file) as OpenedFile[];

    if (newOpenedFiles.length > 0) {
      setOpenedFiles(newOpenedFiles);
      setActiveFileId(newOpenedFiles[0].id);
    }
  }, [exampleSetId, hasCompletedInitialLoad, files]);

  const openFileHandler = useCallback(
    (fileIdToOpen: string, addToFront = false) => {
      if (!files || !files.outputs || !files.previousOutputs || !files.inputs) {
        return;
      }

      const fileInfo =
        files.outputs.find((f) => f.id === fileIdToOpen) ||
        files.previousOutputs.find((f) => f.id === fileIdToOpen) ||
        files.inputs.find((f) => f.id === fileIdToOpen);

      if (fileInfo) {
        setOpenedFiles((prev) => {
          const isAlreadyOpen = prev.some((f) => f.id === fileInfo.id);
          if (!isAlreadyOpen) {
            return addToFront ? [fileInfo, ...prev] : [...prev, fileInfo];
          }
          return prev;
        });

        setSelectedFile(fileInfo);
        setActiveFileId(fileInfo.id);
      }
    },
    [files, setSelectedFile],
  );

  // Loads in file on first page load - only run once
  useEffect(() => {
    // Wait for both the hook to complete loading AND we haven't initialized files yet
    if (!files || hasInitializedFiles || !hasCompletedInitialLoad) {
      return;
    }

    // Add a check to ensure we have valid workflow/task/example IDs
    if (!workflowId || !taskId || !exampleSetId) {
      return;
    }

    const hasAnyNonInputFiles =
      (files.outputs && files.outputs.length > 0) ||
      (files.previousOutputs && files.previousOutputs.length > 0);

    if (hasAnyNonInputFiles) {
      // Priority: Current outputs -> Previous outputs
      const firstFile =
        (files.outputs && files.outputs[0]) || (files.previousOutputs && files.previousOutputs[0]);

      if (firstFile) {
        openFileHandler(firstFile.id);
        setHasInitializedFiles(true);
      }
    }
  }, [
    files,
    hasInitializedFiles,
    hasCompletedInitialLoad,
    openFileHandler,
    workflowId,
    taskId,
    exampleSetId,
  ]);

  useEffect(() => {
    if (!files?.outputs || !hasCompletedInitialLoad || !hasInitializedFiles) {
      return;
    }

    // Check if there's a new output that isn't currently opened
    const newOutput = files.outputs.find(
      (output) => !openedFiles.some((openedFile) => openedFile.id === output.id),
    );

    if (newOutput) {
      console.log(t("components.Builder.autoOpeningNewOutput"), newOutput);
      openFileHandler(newOutput.id, true); // Pass true to add to front
    }
  }, [files?.outputs, openedFiles, hasCompletedInitialLoad, hasInitializedFiles, openFileHandler]);

  const removeOpenedFileHandler = useCallback(
    (fileIdToRemove: string) => {
      setOpenedFiles((prevOpenedFiles) => {
        const indexToRemove = prevOpenedFiles.findIndex((f) => f.id === fileIdToRemove);
        const newOpenedFiles = prevOpenedFiles.filter((f) => f.id !== fileIdToRemove);

        setActiveFileId((prevActiveFileId) => {
          if (fileIdToRemove === prevActiveFileId) {
            if (newOpenedFiles.length > 0) {
              let nextActiveIndex = 0;
              if (indexToRemove === 0 && newOpenedFiles.length > 0) {
                nextActiveIndex = 0;
              } else {
                nextActiveIndex = Math.max(
                  0,
                  Math.min(indexToRemove - 1, newOpenedFiles.length - 1),
                );
              }
              const nextActiveFile = newOpenedFiles[nextActiveIndex];
              setSelectedFile(nextActiveFile);
              return nextActiveFile.id;
            } else {
              setSelectedFile(null);
              return null;
            }
          }
          return prevActiveFileId;
        });

        return newOpenedFiles;
      });
    },
    [setSelectedFile, files],
  );

  const { data: exampleSet, isPending: isExampleSetLoading } = useQuery({
    ...getExampleQuery({
      workflowId,
      taskId,
      exampleSetId,
    }),
    enabled: Boolean(workflowId && taskId && exampleSetId),
  });

  const isDraft = exampleSet?.status === "DRAFT";
  const [tab, setTab] = useState<BuilderSectionTab>("build");
  const [editor, setEditor] = useState<EditorView | null>(null);
  const [isSQLFileLoading, setIsSQLFileLoading] = useState(false);
  const [variableIdMapping, setVariableIdMapping] = useState<Record<string, string>>({});

  const handleVariableInserted = useCallback((variableName: string, taskInputId: string) => {
    setVariableIdMapping((prev) => ({
      ...prev,
      [variableName]: taskInputId,
    }));
  }, []);

  const { data: allFlolakeData, isLoading: isFlolakeDataLoading, refetch } = useFlolakeData();

  const connections = useMemo(() => {
    // Filter connections: must be active and have valid table structure
    const filteredConnections =
      allFlolakeData?.data.connections.filter(
        (connection) =>
          // Check for active connection status
          (connection.connectionInfo?.status === "active" || !connection.connectionInfo?.status) &&
          // Verify table structure exists
          connection.tableData &&
          connection.tableData.length > 0 &&
          // Ensure tables have column definitions
          connection.tableData.every((table) => table.columns && table.columns.length > 0),
      ) || [];

    return filteredConnections;
  }, [allFlolakeData]);

  useEffect(() => {
    if (taskStrategy?.kind === "REVIEW") {
      setTab("details");
    }
  }, [taskStrategy?.kind]);

  const BuilderSectionContent = match(tab)
    .with("build", () =>
      isFlolakeStrategy ? (
        <DataSourcesTree
          connections={connections}
          isFlolakeDataLoading={isFlolakeDataLoading}
          editor={editor}
          isDraft={isDraft}
          refetch={refetch}
        />
      ) : (
        <Chat workflowId={workflowId} taskId={taskId} exampleSetId={exampleSetId} />
      ),
    )
    .with("inputs", () => (
      <Suspense fallback={<InputsTabSection.Pending />}>
        <InputsTabSection
          onOpenFile={openFileHandler}
          editor={editor}
          onVariableInserted={handleVariableInserted}
        />
      </Suspense>
    ))
    .with("versions", () => <VersionsTab data-testid={"builder-versions-tab"} />)
    .with("details", () => (
      <DetailsTab
        workflowId={workflowId}
        taskId={taskId}
        exampleSetId={exampleSetId}
        data-testid={"builder-details-tab"}
      />
    ))
    .run();

  return (
    <Styled.BuilderContainer>
      <Styled.BuilderHeader>
        <Header />
      </Styled.BuilderHeader>
      <Styled.BuilderContent>
        <Styled.StepsSidebar>
          <StepsSidebarList />
          <NewTaskPopover />
        </Styled.StepsSidebar>
        {onInputsPage ? (
          <GlobalInputs />
        ) : (
          <>
            <Styled.BuilderSection>
              <Styled.BuilderSectionContainer>
                {workflowId && taskId && exampleSetId ? (
                  <>
                    <Styled.TopSection>
                      <Styled.BuilderSectionHeader>
                        <Suspense fallback={<BuilderSectionHeader.Pending />}>
                          <BuilderSectionHeader />
                        </Suspense>
                        <Styled.BuilderSectionActions>
                          <Suspense fallback={<BuilderSectionActionBar.Pending />}>
                            <BuilderSectionActionBar />
                          </Suspense>
                        </Styled.BuilderSectionActions>
                      </Styled.BuilderSectionHeader>
                      <BuilderSectionTabs
                        value={tab}
                        onValueChange={setTab}
                        strategy={taskStrategy?.kind}
                      />
                    </Styled.TopSection>
                    <Styled.BuilderSectionContent>
                      {BuilderSectionContent}
                    </Styled.BuilderSectionContent>
                  </>
                ) : // TODO: Get a better null state, also account for Global Inputs
                isRedirecting ? (
                  <Spinner
                    style={{
                      position: "absolute",
                      top: "50%",
                      left: "50%",
                      transform: "translate(-50%, -50%)",
                    }}
                  />
                ) : null}
              </Styled.BuilderSectionContainer>
            </Styled.BuilderSection>
            <Styled.Output>
              {isFlolakeStrategy && (
                <Styled.Half>
                  <SQLEditorSplit
                    connections={connections}
                    editor={editor}
                    setEditor={setEditor}
                    setIsSQLFileLoading={setIsSQLFileLoading}
                    isFlolakeDataLoading={isFlolakeDataLoading}
                    variableIdMapping={variableIdMapping}
                  />
                </Styled.Half>
              )}
              {isFlolakeStrategy ? (
                <Styled.Half>
                  <FilePreview
                    previewName={filePreviewStepNumberText}
                    file={file || null}
                    isLoading={(isFileLoading && isExampleSetLoading) || isSQLFileLoading}
                    mode="builder"
                    example={exampleSet}
                    fileTabs={openedFiles}
                    activeFileId={activeFileId ?? ""}
                    onFileTabClick={openFileHandler}
                    onFileTabClose={removeOpenedFileHandler}
                  />
                </Styled.Half>
              ) : (
                <FilePreview
                  previewName={filePreviewStepNumberText}
                  file={file ?? null}
                  isLoading={(isFileLoading && isExampleSetLoading) || isSQLFileLoading}
                  mode="builder"
                  example={exampleSet}
                  fileTabs={openedFiles}
                  activeFileId={activeFileId ?? ""}
                  onFileTabClick={openFileHandler}
                  onFileTabClose={removeOpenedFileHandler}
                />
              )}
            </Styled.Output>
          </>
        )}
      </Styled.BuilderContent>
    </Styled.BuilderContainer>
  );
};
