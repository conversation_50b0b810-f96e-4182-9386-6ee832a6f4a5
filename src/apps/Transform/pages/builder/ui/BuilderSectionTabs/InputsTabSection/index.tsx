import { match, P } from "ts-pattern";
import { useParams } from "react-router-dom";
import { Button, Divider, Skeleton, useToast } from "@floqastinc/flow-ui_core";
import Add from "@floqastinc/flow-ui_icons/material/Add";
import { TaskSourceSchema } from "@floqastinc/transform-v3/lib/v3/schemas";
import { ExampleInput, TaskInput, ExampleOutput, TaskOutput, Task } from "@floqastinc/transform-v3";
import { useState, useEffect, useRef } from "react";
import { useMutation, useSuspenseQuery, useQueryClient } from "@tanstack/react-query";
import { range } from "es-toolkit";
import { EditorView } from "codemirror";
import * as Styled from "./index.styles";
import { InputCard } from "./components/InputCard";
import { InputForm } from "./components/InputForm";
import { t } from "@/utils/i18n";
import { useSuspenseTasks } from "@v3/tasks";
import { createTaskInput, WorkflowInputData } from "@/api/bff/taskInputs";
import { useFeatureFlags } from "@/components/FeatureFlag";
import { v3, ApiError } from "@/services/v3";
import { InputsDropdown } from "@BuilderV3/routes/workflows/components/InputsDropdown/InputsDropdown";
import { getInputValue } from "@/utils/inputs";
import { SpinnerButton } from "@/components/SpinnerButton";

type InputsTabSectionProps = {
  onOpenFile: (fileIdToOpen: string) => void;
  editor?: EditorView | null;
  onVariableInserted?: (variableName: string, taskInputId: string) => void;
};
export const InputsTabSection = ({
  onOpenFile,
  editor,
  onVariableInserted,
}: InputsTabSectionProps) => {
  const { workflowId = "", taskId = "", exampleSetId = "" } = useParams();
  const { getFlag } = useFeatureFlags();
  const { showToast, Toast } = useToast();
  const queryClient = useQueryClient();

  const tasksQuery = useSuspenseTasks(
    { workflowId },
    {
      enabled: !!workflowId,
    },
  );

  const { data: inputsByType, isFetching } = useSuspenseQuery({
    queryKey: ["inputs", { workflowId, taskId, exampleSetId }],
    queryFn: async () => {
      const results = await Promise.all([
        v3.tasks.getTasks({ workflowId }),
        v3.taskInputs.getTaskInputs({
          workflowId,
          taskId,
        }),
        v3.taskOutputs.getTaskOutputs({ workflowId, taskId }),
        v3.exampleInputs.getExampleInputs({
          workflowId,
          taskId,
          exampleSetId,
        }),
        v3.exampleOutputs.getExampleOutputs({
          workflowId,
          taskId,
          exampleSetId,
        }),
      ]);

      for (const result of results) {
        if (result.errors?.length) {
          throw new ApiError(result.errors);
        }
        if (!result.data) {
          throw new Error(t("components.InputsTabSection.Errors.noDataOrErrors"));
        }
      }
      const [
        tasksResponse,
        taskInputsResponse,
        taskOutputsResponse,
        exampleSetInputsResponse,
        exampleOutputsResponse,
      ] = results;

      return {
        tasks: tasksResponse.data,
        taskInputs: taskInputsResponse.data,
        taskOutputs: taskOutputsResponse.data,
        exampleSetInputs: exampleSetInputsResponse.data,
        exampleOutputs: exampleOutputsResponse.data,
      };
    },
    select: ({ tasks, taskInputs, taskOutputs, exampleSetInputs, exampleOutputs }) => {
      const inputsByType = exampleSetInputs.reduce(
        (acc, exampleInput) => {
          const taskInput = taskInputs.find(
            (taskInput) => taskInput.id === exampleInput.taskInputId,
          );
          if (taskInput && TaskSourceSchema.safeParse(taskInput.source).success) {
            const indexOfTaskForInput = tasks.findIndex(
              (task) =>
                task.id ===
                (TaskSourceSchema.safeParse(taskInput.source).success
                  ? taskInput.source?.taskId
                  : ""),
            );
            if (indexOfTaskForInput !== undefined && indexOfTaskForInput !== -1) {
              acc.previousTaskOutputs.push({
                index: indexOfTaskForInput,
                taskInput: taskInput,
                exampleInput: exampleInput,
              });
            }
          } else if (taskInput) {
            acc.inputs.push({
              taskInput: taskInput,
              exampleInput: exampleInput,
            });
          } else {
            console.warn(t("components.Builder.Errors.couldNotFindTaskInput"), exampleInput);
          }
          return acc;
        },
        {
          previousTaskOutputs: [],
          inputs: [],
          outputs:
            exampleOutputs
              .filter((exampleOutput) => exampleOutput.value)
              .map((exampleOutput) => ({
                exampleOutput,
                taskOutput: taskOutputs?.find((taskOutput) => taskOutput.taskId === taskId),
              })) || [],
        } as {
          previousTaskOutputs: {
            index: number;
            taskInput: TaskInput;
            exampleInput: ExampleInput;
          }[];
          inputs: {
            taskInput: TaskInput;
            exampleInput: ExampleInput;
          }[];
          outputs: {
            exampleOutput: ExampleOutput;
            taskOutput?: TaskOutput;
          }[];
        },
      );

      return inputsByType;
    },
  });

  // Track previous data to detect when temp IDs are replaced with real IDs
  const prevDataRef = useRef<typeof inputsByType | null>(null);

  useEffect(() => {
    if (prevDataRef.current && inputsByType) {
      const prevInputs = prevDataRef.current.inputs;
      const currentInputs = inputsByType.inputs;

      const resolvedInputs = currentInputs.filter((currentInput) => {
        const hadTempId = prevInputs.some(
          (prevInput) =>
            isTempId(prevInput.taskInput.id) &&
            prevInput.taskInput.name === currentInput.taskInput.name &&
            !isTempId(currentInput.taskInput.id),
        );
        return hadTempId;
      });

      if (resolvedInputs.length > 0) {
        onOpenFile(resolvedInputs[0].exampleInput.id);
      }
    }

    prevDataRef.current = inputsByType;
  }, [inputsByType]);

  const shouldShowDivider = match(inputsByType)
    // Has any combination of content sections
    .with(
      P.when((data) => {
        const hasPreviousOutputs = data.previousTaskOutputs.length > 0;
        const hasInputs = data.inputs.length > 0;
        const hasOutputs = data.outputs.length > 0;
        const contentSections = [hasPreviousOutputs, hasInputs, hasOutputs].filter(Boolean);
        return contentSections.length > 1;
      }),
      () => true,
    )
    .otherwise(() => false);

  const isTempId = (id: string): boolean => {
    return id.startsWith("temp-") || id.startsWith("temp-example-");
  };

  const [showNewInputForm, setShowNewInputForm] = useState(false);
  const [newInput, setNewInput] = useState<Partial<WorkflowInputData>>({
    name: "",
    description: "",
    type: undefined,
  });
  const createTaskInputMutation = useMutation({
    mutationKey: ["createTaskInput", { workflowId, taskId, exampleSetId }],
    mutationFn: createTaskInput,
    onMutate: async (variables) => {
      // Prevent any race conditions by canceling any ongoing queries
      await queryClient.cancelQueries({
        queryKey: ["inputs", { workflowId, taskId, exampleSetId }],
      });

      // Snapshot the previous state
      const previousInputs = queryClient.getQueryData([
        "inputs",
        { workflowId, taskId, exampleSetId },
      ]);

      // When the mutation is successful, these temp ids will be replaced with the actual ids
      const optimisticTaskInputId = `temp-${Date.now()}`;
      const optimisticExampleInputId = `temp-example-${Date.now()}`;

      const optimisticTaskInput: TaskInput = {
        id: optimisticTaskInputId,
        workflowId,
        taskId,
        name: variables.input.name || "",
        description: variables.input.description || "",
        type: variables.input.type || "TEXT",
      };

      const optimisticExampleInput: ExampleInput = {
        id: optimisticExampleInputId,
        workflowId,
        taskId,
        exampleSetId,
        taskInputId: optimisticTaskInputId,
        value: variables.input.value
          ? {
              kind: variables.input.type || "TEXT",
              value: variables.input.value,
            }
          : undefined,
      };

      queryClient.setQueryData(
        ["inputs", { workflowId, taskId, exampleSetId }],
        (
          old:
            | {
                tasks: Task[];
                taskInputs: TaskInput[];
                taskOutputs: TaskOutput[];
                exampleSetInputs: ExampleInput[];
                exampleOutputs: ExampleOutput[];
              }
            | undefined,
        ) => {
          if (!old) return old;

          return {
            tasks: old.tasks,
            taskInputs: [...old.taskInputs, optimisticTaskInput],
            taskOutputs: old.taskOutputs,
            exampleSetInputs: [...old.exampleSetInputs, optimisticExampleInput],
            exampleOutputs: old.exampleOutputs,
          };
        },
      );

      setShowNewInputForm(false);
      setNewInput({
        name: "",
        description: "",
        type: undefined,
      });
      return { previousInputs };
    },
    onError: (err, variables, context) => {
      // Rollback the optimistic update if the mutation fails
      if (context?.previousInputs) {
        queryClient.setQueryData(
          ["inputs", { workflowId, taskId, exampleSetId }],
          context.previousInputs,
        );
      }

      showToast(
        <Toast type="error">
          <Toast.Message>
            {t("components.InputsTabSection.Errors.failedCreateInputs")}
          </Toast.Message>
        </Toast>,
      );
    },
    onSuccess: () => {
      setNewInput({
        name: "",
        description: "",
        type: undefined,
      });
    },
    onSettled: () => {
      // Invalidate on pass/fail to ensure the data is always correct.
      // For example, the temp ids will be replaced with the actual ids
      queryClient.invalidateQueries({
        queryKey: ["inputs", { workflowId, taskId, exampleSetId }],
      });
    },
  });

  const [currentlyEditingInput, setCurrentlyEditingInput] = useState<TaskInput | null>(null);

  // Get current task strategy to pass to InputForm
  const currentTask = tasksQuery.data?.find((task) => task.id === taskId);
  const taskStrategy = currentTask?.strategy;

  const insertVariableIntoEditor = (variableName: string, taskInputId: string) => {
    if (editor) {
      const cursorPos = editor.state.selection.main.head;
      const insertPosition =
        cursorPos === -1 ||
        cursorPos === editor.state.doc.length ||
        (cursorPos === 0 && editor.state.doc.length > 0)
          ? editor.state.doc.length
          : cursorPos;

      // Check if there are multiple inputs with the same name
      const inputsWithSameName = inputsByType.inputs.filter(
        ({ taskInput }) => taskInput.name === variableName,
      );

      let displayName = variableName;
      if (inputsWithSameName.length > 1) {
        // Find the index of this specific input among those with the same name
        const inputIndex = inputsWithSameName.findIndex(
          ({ taskInput }) => taskInput.id === taskInputId,
        );
        if (inputIndex >= 0) {
          displayName = `${variableName}_${inputIndex + 1}`;
        }
      }

      const textToInsert = `{$${displayName}}`;
      const finalCursorPosition = insertPosition + textToInsert.length;
      editor.dispatch({
        changes: {
          from: insertPosition,
          to: insertPosition,
          insert: textToInsert,
        },
        selection: {
          anchor: finalCursorPosition,
          head: finalCursorPosition,
        },
      });
      editor.focus();
    }

    // Store the mapping using the actual display name that was inserted
    if (onVariableInserted) {
      // Calculate the same display name logic
      const inputsWithSameName = inputsByType.inputs.filter(
        ({ taskInput }) => taskInput.name === variableName,
      );

      let displayName = variableName;
      if (inputsWithSameName.length > 1) {
        const inputIndex = inputsWithSameName.findIndex(
          ({ taskInput }) => taskInput.id === taskInputId,
        );
        if (inputIndex >= 0) {
          displayName = `${variableName}_${inputIndex + 1}`;
        }
      }

      onVariableInserted(displayName, taskInputId);
    }
  };

  return (
    <Styled.InputsTabContainer>
      <Styled.InputTabHeader>
        <Button
          variant="outlined"
          color="dark"
          onClick={() => {
            setShowNewInputForm(true);
          }}
          style={{
            alignSelf: "start",
          }}
          data-testid="add-input-button"
        >
          <Add color="currentColor" />
          {t("components.InputsTabSection.addInput")}
        </Button>
        <InputsDropdown />
      </Styled.InputTabHeader>
      {showNewInputForm ? (
        <InputForm
          title="New Input"
          input={newInput}
          mode="create"
          taskStrategy={taskStrategy}
          onSave={(newInput) => {
            createTaskInputMutation.mutate({
              workflowId,
              taskId,
              exampleSetId,
              input: newInput,
            });
          }}
          onChange={(input) => {
            setNewInput({
              ...newInput,
              ...input,
            });
          }}
          onCancel={() => {
            setShowNewInputForm(false);
          }}
        />
      ) : null}

      {/* Current task outputs */}
      {inputsByType.outputs.map(({ exampleOutput, taskOutput }) => (
        <InputCard
          key={exampleOutput.id}
          name={taskOutput?.name || t("components.InputsTabSection.currentOutput")}
          secondaryText={t("components.InputsTabSection.currentOutput")}
          type="OUTPUT"
          actions={() => {
            return (
              <Button
                variant="outlined"
                color="dark"
                size="sm"
                onClick={() => {
                  onOpenFile(exampleOutput.id);
                }}
              >
                {t("components.InputsTabSection.open")}
              </Button>
            );
          }}
        />
      ))}

      {/* Previous task outputs */}
      {Object.entries(inputsByType.previousTaskOutputs).map(
        ([taskInputId, { index, exampleInput }]) => {
          return (
            <InputCard
              key={taskInputId}
              name={tasksQuery.data?.[index].name ?? ""}
              secondaryText={t("components.InputsTabSection.stepOutput", { index: index + 1 })}
              type="OUTPUT"
              actions={() => {
                return (
                  <Button
                    variant="outlined"
                    color="dark"
                    size="sm"
                    onClick={() => {
                      onOpenFile(exampleInput.id);
                    }}
                  >
                    {t("components.InputsTabSection.open")}
                  </Button>
                );
              }}
            />
          );
        },
      )}

      {shouldShowDivider && inputsByType.inputs.length > 0 ? <Divider /> : null}
      {/* All the inputs for current task */}
      {/* @TODO: Update this to use InputsList */}
      {inputsByType.inputs.map(({ taskInput, exampleInput }) =>
        currentlyEditingInput?.id === taskInput.id ? (
          <InputForm
            key={taskInput.id}
            title={taskInput.name}
            input={{
              name: taskInput.name,
              description: taskInput.description,
              type: taskInput.type,
              value: getInputValue(exampleInput),
            }}
            mode="edit"
            taskStrategy={taskStrategy}
            onSave={(_input) => {
              setCurrentlyEditingInput(null);
              // TODO: implement input editing
            }}
            onChange={(input) => {
              setCurrentlyEditingInput({
                ...taskInput,
                ...input,
              });
            }}
            onCancel={() => {
              setCurrentlyEditingInput(null);
            }}
          />
        ) : (
          <InputCard
            key={taskInput.id}
            name={taskInput.name}
            secondaryText={getInputValue(exampleInput)?.toString() ?? ""}
            type={taskInput.type}
            actions={() => {
              return (
                <Styled.InputButtonContainer>
                  <div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
                    {getFlag("enable-builder-input-editing") ? (
                      <Button
                        variant="ghost"
                        color="dark"
                        size="sm"
                        onClick={() => {
                          setCurrentlyEditingInput(taskInput);
                        }}
                      >
                        {t("components.InputsTabSection.edit")}
                      </Button>
                    ) : null}
                    {editor && taskStrategy?.kind === "FLOLAKE" && taskInput.type !== "FILE" && (
                      <Button
                        variant="ghost"
                        color="dark"
                        onClick={() => insertVariableIntoEditor(taskInput.name, taskInput.id)}
                        style={{
                          minWidth: "32px",
                          padding: "6px",
                        }}
                        data-testid="insert-variable-button"
                      >
                        <Add color="currentColor" />
                      </Button>
                    )}
                  </div>
                  {taskInput.type === "FILE" ? (
                    <SpinnerButton
                      variant="outlined"
                      color="dark"
                      size="sm"
                      isPending={
                        isTempId(taskInput.id)
                          ? createTaskInputMutation.isPending || isFetching
                          : false
                      }
                      disabled={
                        isTempId(taskInput.id)
                          ? createTaskInputMutation.isPending || isFetching
                          : false
                      }
                      onClick={() => {
                        onOpenFile(exampleInput.id);
                      }}
                    >
                      {t("components.InputsTabSection.open")}
                    </SpinnerButton>
                  ) : null}
                </Styled.InputButtonContainer>
              );
            }}
          />
        ),
      )}
    </Styled.InputsTabContainer>
  );
};

const Pending = () => {
  return (
    <Styled.InputsTabContainer>
      <Skeleton variant="rectangle" width="100%" height="58px" />
      <Divider />
      {range(0, 5).map((i) => (
        <Skeleton key={i} variant="rectangle" width="100%" height="58px" />
      ))}
    </Styled.InputsTabContainer>
  );
};

InputsTabSection.Pending = Pending;
