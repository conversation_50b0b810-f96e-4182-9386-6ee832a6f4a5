import { FQIntl } from "@floqastinc/fq-intl";
import { describe, expect, it, vi, beforeEach } from "vitest";
import { type Task } from "@floqastinc/transform-v3";
import { useSuspenseQuery } from "@tanstack/react-query";
import { userEvent } from "@vitest/browser/context";
import { InputsTabSection } from ".";
import { useSuspenseTasks } from "@v3/tasks";
import { customRender, createBrowserWrapper } from "@/utils/testing";

vi.mock("@/components/FeatureFlag", () => ({
  useFeatureFlags: () => ({
    getFlag: (flag: string) => flag === "enable-builder-input-editing",
  }),
  featureFlags: {
    get: vi.fn(),
    set: vi.fn(),
    remove: vi.fn(),
  },
}));

vi.mock("@v3/tasks");

vi.mock("@tanstack/react-query", async () => {
  const actual = await vi.importActual("@tanstack/react-query");
  return {
    ...actual,
    useSuspenseQuery: vi.fn(),
  };
});

vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useParams: () => ({
      workflowId: "mock-workflow-id",
      taskId: "mock-task-id",
      exampleSetId: "mock-example-set-id",
    }),
    useNavigate: () => vi.fn(),
  };
});

const mockUseSuspenseQuery = vi.mocked(useSuspenseQuery);
const mockOnOpenFile = vi.fn();

vi.mock("@floqastinc/fq-intl", () => {
  return {
    FQIntl: {
      DateTimeFormat: vi.fn(),
      initializeForFrontend: vi.fn(),
    },
  };
});
const mockedDateTimeFormat = vi.mocked(FQIntl.DateTimeFormat);
const mockFormat = vi.fn();

const renderWithDate = (dateValue: string | null, expectedFormat: string) => {
  const mockInstance = {
    format: mockFormat.mockImplementation(() => expectedFormat),
    formatToParts: vi.fn(),
    resolvedOptions: vi.fn(),
    resolvedIsoOptions: vi.fn(),
    formatRange: vi.fn(),
    formatRangeToParts: vi.fn(),
  } as unknown as InstanceType<typeof FQIntl.DateTimeFormat>;
  mockedDateTimeFormat.mockImplementation(() => mockInstance);

  mockUseSuspenseQuery.mockReturnValue({
    data: {
      inputs: [
        {
          taskInput: {
            id: "date-input",
            name: "Date Input",
            type: "DATETIME",
          },
          exampleInput: {
            value: dateValue
              ? {
                  kind: "DATETIME",
                  value: dateValue,
                }
              : null,
          },
        },
      ],
      previousTaskOutputs: [],
      outputs: [],
    },
    error: null,
    isError: false,
    isPending: false,
    isLoading: false,
    isSuccess: true,
    status: "success",
    fetchStatus: "idle",
    refetch: vi.fn(),
  } as unknown as ReturnType<typeof useSuspenseQuery>);

  return customRender(<InputsTabSection onOpenFile={mockOnOpenFile} />, {
    wrapper: createBrowserWrapper(),
  });
};

const renderWithFile = (fileId: string | null) => {
  mockUseSuspenseQuery.mockReturnValue({
    data: {
      inputs: [
        {
          taskInput: {
            id: "file-input",
            name: "File Input",
            type: "FILE",
          },
          exampleInput: {
            value: fileId
              ? {
                  kind: "FILE",
                  value: fileId,
                }
              : null,
          },
        },
      ],
      previousTaskOutputs: [],
      outputs: [],
    },
    error: null,
    isError: false,
    isPending: false,
    isLoading: false,
    isSuccess: true,
    status: "success",
    fetchStatus: "idle",
    refetch: vi.fn(),
  } as unknown as ReturnType<typeof useSuspenseQuery>);

  return customRender(<InputsTabSection onOpenFile={mockOnOpenFile} />, {
    wrapper: createBrowserWrapper(),
  });
};

const mockWorkflowId = "mock-workflow-id";
const mockTaskId = "mock-task-id";
vi.mocked(useSuspenseTasks).mockReturnValue({
  data: [{ id: mockTaskId, name: "Mocked Task", workflowId: mockWorkflowId }] as Task[],
  error: null,
  isError: false,
  isPending: false,
  isLoading: false,
  isSuccess: true,
  status: "success",
  fetchStatus: "idle",
  refetch: vi.fn(),
} as unknown as ReturnType<typeof useSuspenseTasks>);

beforeEach(() => {
  vi.clearAllMocks();
});

describe("InputsTabSection", () => {
  it("When ISO date provided, should display in correct format", () => {
    const expectedFormat = "6/3/2025";
    const screen = renderWithDate("2025-06-03T00:00:00.000Z", expectedFormat);
    expect(screen.getByText(expectedFormat)).toBeInTheDocument();
  });

  it("When invalid date string provided, should display gracefully", () => {
    const expectedFormat = "Invalid Date";
    const screen = renderWithDate("not-a-valid-date", expectedFormat);
    expect(screen.getByText(expectedFormat)).toBeInTheDocument();
  });

  it("When add input button is pressed, should open input form in create mode", async () => {
    const screen = customRender(<InputsTabSection onOpenFile={mockOnOpenFile} />);
    await userEvent.click(screen.getByTestId("add-input-button"));
    expect(screen.getByText("New Input")).toBeVisible();
  });

  it("When edit button is pressed, should open input form in edit mode", async () => {
    const screen = renderWithFile("file-i");
    await userEvent.click(screen.getByRole("button", { name: "components.InputsTabSection.edit" }));
    expect(screen.getByRole("button", { name: "components.InputForm.delete" })).toBeVisible();
  });
});
