import { Children, isValidElement } from "react";
import Markdown from "react-markdown";
import { Heading, Skeleton } from "@floqastinc/flow-ui_core";
import { useMutation, useQuery } from "@tanstack/react-query";
import * as Styled from "./styles";
import { v3, ApiError } from "@/services/v3";
import { queryClient } from "@/components";
import { t } from "@/utils/i18n";

export type SubStep = { heading: string; details: string };

const SkeletonStepDetails = () =>
  [1, 2, 3, 4, 5].map((i) => (
    <Styled.DetailStep key={i}>
      <Styled.DetailStepName>
        <Skeleton lines={1} width="50%" />
      </Styled.DetailStepName>
      <Styled.DetailStepDescription>
        <Skeleton lines={4} width="100%" />
      </Styled.DetailStepDescription>
    </Styled.DetailStep>
  ));

const Li = ({
  children,
  ...rest
}: React.DetailedHTMLProps<React.LiHTMLAttributes<HTMLLIElement>, HTMLLIElement>) => {
  type ParagraphProps = { children: React.ReactNode };
  const children_: React.ReactNode[] = [];
  Children.forEach(children, (child) => {
    if (isValidElement(child) && child.type === "p") {
      children_.push((child as React.ReactElement<ParagraphProps>).props.children);
    } else {
      children_.push(child);
    }
  });
  return <li {...rest}>{children_}</li>;
};

const DetailStepList = ({ steps }: { steps: SubStep[] }) => (
  <>
    {steps.map((subStep, i) => (
      <Styled.DetailStep key={subStep.heading}>
        <Styled.DetailStepName>
          <Heading variant="h5" weight="medium">
            {t("components.DetailSteps.subStepHeading", {
              index: i + 1,
              heading: subStep.heading,
            })}
          </Heading>
        </Styled.DetailStepName>
        <Styled.DetailStepDescription>
          <Markdown
            components={{
              li({ node: _node, ...rest }) {
                return <Li {...rest} />;
              },
            }}
          >
            {subStep.details}
          </Markdown>
        </Styled.DetailStepDescription>
      </Styled.DetailStep>
    ))}
  </>
);

type DetailStepsProps = {
  workflowId: string;
  taskId: string;
  exampleSetId: string;
  isOpen: boolean;
};

export const DetailSteps = ({ workflowId, taskId, exampleSetId, isOpen }: DetailStepsProps) => {
  const createTaskDescriptionMutation = useMutation({
    mutationKey: ["taskDescription", "create", { workflowId, taskId }],
    mutationFn: async ({ exampleSetId }: { exampleSetId: string }) => {
      const response = await v3.taskDescriptions.createTaskDescription({
        workflowId,
        taskId,
        exampleSetId,
        description: { generate: true },
      });
      if (response.errors.length > 0) throw new ApiError(response.errors);
      if (!response.data)
        throw new Error("Unexpected data error: no task description body returned.");
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["taskDescription", workflowId, taskId] });
    },
  });

  const getDescriptionQuery = useQuery({
    queryKey: ["taskDescription", workflowId, taskId, exampleSetId],
    queryFn: async () => {
      const response = await v3.taskDescriptions.getTaskDescriptionByCompositeKey({
        workflowId,
        taskId,
        exampleSetId,
      });

      if (response.errors.length > 0) {
        if (response.errors[0].code === "NOT_FOUND") {
          createTaskDescriptionMutation.mutate({ exampleSetId });
        }
        throw new ApiError(response.errors);
      }

      if (!response.data)
        throw new Error("Unexpected data error: no task description body returned.");

      if (!response.data.id) {
        createTaskDescriptionMutation.mutate({ exampleSetId });
      }

      return response.data;
    },
    enabled: isOpen,
    retry: false,
  });

  if (
    getDescriptionQuery.isPending ||
    getDescriptionQuery.isFetching ||
    createTaskDescriptionMutation.isPending
  ) {
    return <SkeletonStepDetails />;
  }

  return <DetailStepList steps={getDescriptionQuery.data?.description ?? []} />;
};
