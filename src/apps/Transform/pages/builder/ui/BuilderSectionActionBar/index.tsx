import { useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { useMutationState } from "@tanstack/react-query";
import { Skeleton, Tooltip, DropdownPanel, IconButton } from "@floqastinc/flow-ui_core";
import MoreVert from "@floqastinc/flow-ui_icons/material/MoreVert";
import { noop } from "es-toolkit";
import { match } from "ts-pattern";

import { t } from "@/utils/i18n";
import { getIsChatBasedStrategy } from "@/utils/strategy";
import { SpinnerButton } from "@/components/SpinnerButton";
import { useCreateExampleAndNavigate } from "@/hooks/useCreateExampleAndNavigate";
import { useSuspenseExampleOutputs } from "@v3/example-outputs";
import {
  useDeleteExample,
  useSuspenseExample,
  useSuspenseExamples,
  useUpdateExample,
} from "@v3/examples";
import { useTaskQueries } from "@BuilderV3/routes/workflows/BuilderPage.hooks";
import { ConfirmStepDeletionDialog } from "@BuilderV3/app-components/Steps/ConfirmStepDeletionDialog";

/**
 * Contains Cancel, Save, Edit (exampleSet) and Delete (Step) actions.
 *
 * ## Cancel/Save:
 * Cancel only shows up if there's more than one exampleSet. Since
 *  a draft is equivalent to an exampleSet, deleting the last one will put
 *  the application in a weird spot. All steps always start off with one draft exampleSet.
 *
 * Save shows up if the exampleSet is in DRAFT status. Saving an exampleSet changes
 *  it to PUBLISHED.
 *
 * Edit shows up if the exampleSet is in a PUBLISHED/ACTIVE status. Clicking on edit
 *  will create a new draft/version (exampleSet) in the DRAFT status.
 *  This will consequently show the Cancel/Save buttons in the place of the Edit button.
 *
 * In all cases, the kebab MoreVert icon will show up containing the dropdown option
 *  to delete the entire Step itself. Previously, deleting the last step was not allowed,
 *  but currently and going forward the application allows for this to be the case and for
 *  the user to start from just the global inputs/Data Bank.
 */
export const BuilderSectionActionBar = () => {
  const { workflowId = "", taskId = "", exampleSetId = "" } = useParams();
  const { data: exampleSet } = useSuspenseExample(
    {
      workflowId,
      taskId,
      exampleSetId,
    },
    {
      enabled: !!workflowId && !!taskId && !!exampleSetId,
    },
  );
  const { data: examples } = useSuspenseExamples(
    {
      workflowId,
      taskId,
    },
    {
      enabled: !!workflowId && !!taskId,
    },
  );

  const isOnlyExampleSet = examples.length === 1;

  const { data: exampleOutputs } = useSuspenseExampleOutputs(
    {
      workflowId,
      taskId,
      exampleSetId,
    },
    {
      enabled: !!workflowId && !!taskId && !!exampleSetId,
    },
  );
  const { currentTask, tasksQuery } = useTaskQueries();
  const taskStrategy = currentTask?.strategy;
  const isReviewStrategy = taskStrategy?.kind === "REVIEW";
  const isFlolakeStrategy = taskStrategy?.kind === "FLOLAKE";
  const isChatBasedStrategy = getIsChatBasedStrategy(taskStrategy);
  const { handleTaskNameEdit } = useTaskQueries();
  const isPublishable = exampleOutputs.every((exampleOutput) => !!exampleOutput.value);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const deleteExampleSetMutation = useDeleteExample();
  const updateExampleSetMutation = useUpdateExample();
  const { data: tasks } = tasksQuery;

  const { isLatestStep } = useMemo(() => {
    if (!tasks) {
      return {
        isLatestStep: false,
      };
    }
    const index = tasks.findIndex((task) => task.id === taskId);
    return {
      isLatestStep: index === tasks.length - 1,
    };
  }, [tasks, taskId]);
  const isEditing = exampleSet.status === "DRAFT";

  const deleteTaskStatus = useMutationState({
    filters: {
      status: "pending",
      mutationKey: ["deleteTask", { workflowId, taskId }],
    },
    select: ({ state }) => {
      return state.status;
    },
  });

  const { createExampleAndNavigate, isPending: isCreateExamplePending } =
    useCreateExampleAndNavigate();

  const handleEdit = async (copySelectedVersion: boolean) => {
    createExampleAndNavigate({
      copySelectedVersion,
    });
  };

  const getDeleteStepAndAfterValue = () => {
    if (!isLatestStep) {
      return "delete-step-and-after";
    }
    return "delete-step";
  };

  const renderActionsDropdown = () => (
    <DropdownPanel
      disableFilter
      disableClear
      onChange={(value: string) => {
        match(value)
          .with("rename", () => {
            handleTaskNameEdit();
          })
          .with("new-draft-cleared-chat", () => {
            handleEdit(false);
          })
          .with("delete-step-and-after", () => {
            setIsDeleteDialogOpen(true);
          })
          .with("delete-step", () => {
            setIsDeleteDialogOpen(true);
          });
      }}
    >
      <DropdownPanel.Trigger>
        <IconButton size="md" onClick={() => {}}>
          <MoreVert color="#1D2433" data-testid="builder-more-vert-icon" />
        </IconButton>
      </DropdownPanel.Trigger>
      <DropdownPanel.Content
        size={"sm"}
        style={{ position: "absolute", right: "-30px", zIndex: 2 }}
      >
        <DropdownPanel.Group>
          <DropdownPanel.Option
            value="rename"
            key="rename"
            data-testid="builder-edit-task-name-option"
          >
            {t("components.BuilderSectionActionBar.rename")}
          </DropdownPanel.Option>
          <DropdownPanel.Option
            value="new-draft-cleared-chat"
            key="new-draft-cleared-chat"
            data-tracking-id="builder-edit-option-new"
            disabled={isReviewStrategy || isFlolakeStrategy}
          >
            {t("components.BuilderSectionActionBar.createBlankVersion")}
          </DropdownPanel.Option>
        </DropdownPanel.Group>
        {
          <DropdownPanel.Option
            value={getDeleteStepAndAfterValue()}
            key={getDeleteStepAndAfterValue()}
            disabled={!tasks}
            data-testid={
              isLatestStep ? "builder-delete-step-option" : "builder-delete-and-after-step-option"
            }
          >
            {isLatestStep
              ? t("components.BuilderSectionActionBar.deleteStep")
              : t("components.BuilderSectionActionBar.deleteStepAfter")}
          </DropdownPanel.Option>
        }
      </DropdownPanel.Content>
    </DropdownPanel>
  );

  return (
    <>
      {currentTask && (
        <div style={{ position: "absolute" }}>
          <ConfirmStepDeletionDialog
            isOpen={isDeleteDialogOpen}
            task={currentTask}
            onConfirm={() => {
              setIsDeleteDialogOpen(false);
            }}
            onOpenChange={setIsDeleteDialogOpen}
          />
        </div>
      )}
      {isEditing ? (
        <>
          {!isPublishable ? (
            <DisabledSaveButton />
          ) : (
            <SpinnerButton
              variant="outlined"
              color="dark"
              size="sm"
              disabled={
                !isPublishable ||
                deleteExampleSetMutation.isPending ||
                updateExampleSetMutation.isPending ||
                deleteTaskStatus.some((status) => status === "pending")
              }
              isPending={updateExampleSetMutation.isPending}
              onClick={() => {
                if (isOnlyExampleSet) {
                  // If the example set is the only one, we can default to making
                  //  it the active one. This simplifies the UX by making it so
                  //  that the user doesn't have to manually flip it to active
                  //  to create a new step (or run the agent).
                  updateExampleSetMutation.mutate({
                    workflowId,
                    taskId,
                    exampleSetId,
                    example: {
                      status: "ACTIVE",
                    },
                  });
                } else {
                  updateExampleSetMutation.mutate({
                    workflowId,
                    taskId,
                    exampleSetId,
                    example: {
                      status: "PUBLISHED",
                    },
                  });
                }
              }}
            >
              {t("components.BuilderSectionActionBar.actionBarSave")}
            </SpinnerButton>
          )}
          {renderActionsDropdown()}
        </>
      ) : (
        <>
          <SpinnerButton
            variant="outlined"
            color="dark"
            size="sm"
            onClick={() => {
              handleEdit(true);
            }}
            disabled={(!isChatBasedStrategy && !isFlolakeStrategy) || isCreateExamplePending}
            data-tracking-id="builder-edit-button-copy"
            isPending={isCreateExamplePending}
          >
            {t("components.BuilderSectionActionBar.actionBarEdit")}
          </SpinnerButton>
          {renderActionsDropdown()}
        </>
      )}
    </>
  );
};

const Pending = () => {
  return <Skeleton preset="button" height={16} width={50} />;
};

const DisabledSaveButton = () => {
  return (
    <Tooltip>
      <Tooltip.Trigger>
        <SpinnerButton
          variant="outlined"
          color="dark"
          size="sm"
          disabled={true}
          isPending={false}
          onClick={noop}
        >
          {t("components.BuilderSectionActionBar.save")}
        </SpinnerButton>
      </Tooltip.Trigger>
      <Tooltip.Content hasArrow style={{ zIndex: 9001 }}>
        {t("components.BuilderSectionActionBar.draftOneOutput")}
      </Tooltip.Content>
    </Tooltip>
  );
};

BuilderSectionActionBar.Pending = Pending;
