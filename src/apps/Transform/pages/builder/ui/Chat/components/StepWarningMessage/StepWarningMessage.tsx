import * as styled from "./styles";
import WarningIcon from "@/components/WarningIcon";
import { DISMISS_STEP_WARNING_CHAT_QUERY_PARAM } from "@/constants";
import { useCreateExampleAndNavigate } from "@/hooks/useCreateExampleAndNavigate";

export const StepWarningMessage = () => {
  const { createExampleAndNavigate, isPending } = useCreateExampleAndNavigate();
  const handleClick = () => {
    createExampleAndNavigate({
      copySelectedVersion: true,
      searchParams: new URLSearchParams({
        [DISMISS_STEP_WARNING_CHAT_QUERY_PARAM]: "true",
      }),
    });
  };
  return (
    <styled.WarningChat>
      <WarningIcon /> This step failed.
      <styled.Button variant="link" onClick={handleClick} disabled={isPending} loading={isPending}>
        Edit to try again.
      </styled.Button>
    </styled.WarningChat>
  );
};
