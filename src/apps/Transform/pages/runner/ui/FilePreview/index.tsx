import * as xlsx from "xlsx";
import { AgGridReact } from "ag-grid-react";
import { AllCommunityModule, type ColDef, ModuleRegistry, themeQuartz } from "ag-grid-community";
ModuleRegistry.registerModules([AllCommunityModule]);
import {
  Heading,
  IconButton,
  Text,
  EmptyState,
  TableStatusBadge,
  LinkButton,
  Tooltip,
} from "@floqastinc/flow-ui_core";
import Clear from "@floqastinc/flow-ui_icons/legacy/Clear";
import Download from "@floqastinc/flow-ui_icons/material/Download";
import { useEffect, useState, useRef, useMemo } from "react";
import { ExampleSet, ExampleSetStatus } from "@floqastinc/transform-v3";
import { useParams } from "react-router-dom";
import * as Styled from "./index.styles";
import { t } from "@/utils/i18n";
import { downloadFile } from "@/utils/browser";
import { Loading } from "@/components/Loading";
import { StatusChangeDialog } from "@Transform/pages/builder/ui/Chat/components/StatusChange/StatusChange";

const gridFilePreviewTheme = themeQuartz.withParams({
  columnBorder: { style: "solid", color: "#cccccc" },
  headerColumnBorder: { style: "solid", color: "#cccccc" },
  rowBorder: { style: "solid", color: "#cccccc" },
  headerRowBorder: { style: "solid", color: "#cccccc" },
  headerBackgroundColor: "#EDEDED",
});

const parseExcel = async (file: File): Promise<xlsx.WorkBook> => {
  const fileBuffer = await file.arrayBuffer();
  const wb = xlsx.read(fileBuffer);
  return wb;
};

const getColumnLettersFromRange = (range: string | undefined) => {
  if (!range) return [];

  const match = range.match(/^([A-Z]+)\d+:([A-Z]+)\d+$/);
  if (!match) return [];
  const [_, __, endCol] = match;

  const endNum = xlsx.utils.decode_col(endCol);

  // Create an array of column letters from A to the last column
  return Array.from({ length: endNum + 1 }, (_, i) => xlsx.utils.encode_col(i));
};

export type FilePreviewMode = "builder" | "runner";

type FileTab = {
  id: string;
  name: string;
  displayName: string;
  type?: "Input" | "Output" | "PreviousOutput";
  stepNumber?: number;
};

type FilePreviewProps = {
  file: File | null;
  isLoading: boolean;
  mode: FilePreviewMode;
  previewName: string;
};

type FilePreviewBuilderProps = FilePreviewProps & {
  mode: "builder";
  example: ExampleSet | undefined;
  fileTabs: FileTab[];
  activeFileId: string;
  onFileTabClick?: (fileId: string) => void;
  onFileTabClose?: (fileId: string) => void;
};

type FilePreviewRunnerProps = FilePreviewProps & {
  mode: "runner";
};

export const FilePreview = (props: FilePreviewBuilderProps | FilePreviewRunnerProps) => {
  const { file, isLoading, mode, previewName } = props;

  const example = props.mode === "builder" ? props.example : undefined;
  const fileTabs = props.mode === "builder" ? props.fileTabs : [];
  const activeFileId = props.mode === "builder" ? props.activeFileId : "";
  const onFileTabClick = props.mode === "builder" ? props.onFileTabClick : undefined;
  const onFileTabClose = props.mode === "builder" ? props.onFileTabClose : undefined;
  const [sheets, setSheets] = useState<{
    [sheetName: string]: xlsx.WorkSheet;
  } | null>();
  const [activeTab, setActiveTab] = useState("");
  const [columnDefs, setColumnDefs] = useState<ColDef<{ [key: string]: unknown }>[]>([]);
  const [, setContainerSize] = useState({ width: 0, height: 0 });
  const [processingState, setProcessingState] = useState<"not-started" | "processing" | "finished">(
    "not-started",
  );
  const [statusChange, setStatusChange] = useState<{
    fromStatus: ExampleSetStatus | null;
    toStatus: ExampleSetStatus | null;
    exampleSetId: string | null;
  }>({
    fromStatus: null,
    toStatus: null,
    exampleSetId: null,
  });
  const isStatusChangeDialogOpen = Boolean(
    statusChange.fromStatus && statusChange.toStatus && statusChange.exampleSetId,
  );
  const { workflowId, taskId } = useParams();
  // Refs for container measurements
  const gridContainerRef = useRef<HTMLDivElement>(null);
  const gridRef = useRef<AgGridReact>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  useEffect(() => {
    const parseFile = async () => {
      setProcessingState("not-started");
      if (!file) return;

      const isExcel =
        file.name.endsWith(".xlsx") ||
        file.name.endsWith(".csv") ||
        file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

      try {
        setProcessingState("processing");
        const data = isExcel ? await parseExcel(file) : null;
        if (data?.Sheets) {
          setSheets(data.Sheets);
          const firstSheet = Object.keys(data.Sheets)[0];
          setActiveTab(firstSheet);
        }
      } catch {
        // NOTE: Only setting finished on `catch`, not on `finally`.
        // Updates here trigger another useEffect where processExcelSheets is called
        setProcessingState("finished");
      }
    };

    parseFile();
  }, [file]);

  // Set up ResizeObserver to monitor container size changes
  useEffect(() => {
    const currentRef = gridContainerRef.current; // Capture ref value
    if (!currentRef) return;

    // Clean up previous observer if it exists
    if (resizeObserverRef.current) {
      resizeObserverRef.current.disconnect();
    }

    // Create new observer
    resizeObserverRef.current = new ResizeObserver((entries) => {
      const entry = entries[0];
      if (entry) {
        const { width, height } = entry.contentRect;
        setContainerSize({ width, height });
      }
    });

    // Start observing
    resizeObserverRef.current.observe(currentRef);

    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, []);

  // Memoized data processing for better performance
  const { processedRowData, memoizedColumnDefs } = useMemo(() => {
    if (!sheets || !activeTab || !sheets[activeTab]) {
      return { processedRowData: [], memoizedColumnDefs: [] };
    }

    try {
      const sheet = sheets[activeTab];
      const range = getColumnLettersFromRange(sheet["!ref"]);
      const sheetJson = xlsx.utils.sheet_to_json<Record<string, unknown>>(sheet, {
        header: "A",
        defval: "",
        blankrows: true,
        raw: false,
      });

      // Process all data with row numbers - memoized for performance
      const processedData = sheetJson.map((row, i) => ({ ...row, __: i + 1 }));

      // Dynamically calculate row # column width
      const totalRows = processedData.length;
      const getRowNumberColumnWidth = (rowCount: number) => {
        const digits = Math.floor(Math.log10(rowCount)) + 1;

        const baseWidth = 24;
        const digitWidth = 8;

        return Math.max(32, baseWidth + digits * digitWidth);
      };

      // Memoized column definitions
      const columnDefinitions = [
        {
          headerName: " ",
          field: "__",
          width: getRowNumberColumnWidth(totalRows),
          sortable: false,
          pinned: "left" as const,
          cellStyle: {
            backgroundColor: "#EDEDED",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          },
        },
        ...range.map((col: string) => ({
          field: col,
          sortable: false,
        })),
      ];

      return {
        processedRowData: processedData,
        memoizedColumnDefs: columnDefinitions,
      };
    } catch {
      return { processedRowData: [], memoizedColumnDefs: [] };
    }
  }, [activeTab, sheets]);

  // Update column defs when memoized data changes
  useEffect(() => {
    setColumnDefs(memoizedColumnDefs);
    if (processingState === "processing") {
      setProcessingState("finished");
    }
  }, [memoizedColumnDefs]);

  // Only show loading for initial file load or when explicitly loading
  const isCurrentlyLoading = isLoading || processingState === "processing";

  // Add this check to prevent showing empty state when switching files
  const shouldShowEmptyState = !isCurrentlyLoading && !file && !isLoading;

  const allOutputFileTabs = fileTabs.filter((f) => f.type !== "Input");
  const currentOutputFileExists = allOutputFileTabs.some((f) => f.type === "Output");
  const baseHeaderFileTab = currentOutputFileExists
    ? fileTabs.find((f) => f.type === "Output")
    : fileTabs.find((f) => f.type === "PreviousOutput");
  const clearableFileTabs = currentOutputFileExists
    ? fileTabs.filter((f) => f.type !== "Output")
    : fileTabs.filter((f) => f.type === "Input");

  if (isCurrentlyLoading) {
    return <Loading />;
  }

  if (shouldShowEmptyState) {
    return (
      <Styled.EmptyFilePreview data-testid="empty-file-preview">
        <EmptyState />
        <div>
          <Text weight={5} size={5} lineHeight={4}>
            {t("components.FilePreview.noDataPreview")}
          </Text>
        </div>
      </Styled.EmptyFilePreview>
    );
  }

  const statusColor = {
    ACTIVE: "success",
    PUBLISHED: "info",
    DRAFT: "default",
    ARCHIVED: "default",
    OUTDATED: "default",
  };

  return (
    <Styled.FilePreviewPanel>
      <StatusChangeDialog
        isStatusChangeDialogOpen={isStatusChangeDialogOpen}
        statusChange={statusChange}
        setStatusChange={setStatusChange}
        workflowId={workflowId || ""}
        taskId={taskId || ""}
        makeActive={statusChange.fromStatus === "PUBLISHED" && statusChange.toStatus === "ACTIVE"}
      />
      <Styled.FilePreviewHeader>
        {!isCurrentlyLoading && file ? (
          <>
            <Styled.FilePreviewHeaderLeft>
              {mode === "runner" ? (
                <Styled.FilePreviewMetaData>
                  <Styled.StepName mode={mode}>
                    <Heading variant="body-base" weight="medium">
                      {previewName}
                    </Heading>
                  </Styled.StepName>
                </Styled.FilePreviewMetaData>
              ) : (
                <Styled.FileTabs>
                  {baseHeaderFileTab && (
                    <Styled.FileTab
                      active={baseHeaderFileTab.id === activeFileId}
                      hasCloseButton={false}
                      onClick={() => {
                        onFileTabClick?.(baseHeaderFileTab.id);
                      }}
                    >
                      <Styled.StepName mode={mode}>
                        <Heading variant="body-base" weight="medium">
                          {previewName}
                        </Heading>
                      </Styled.StepName>
                      {mode === "builder" && (
                        <TableStatusBadge
                          style={{
                            backgroundColor:
                              baseHeaderFileTab.id === activeFileId ? "#EDEDED" : "transparent",
                          }}
                          hasIcon={false}
                          truncateText={false}
                          color={example ? statusColor[example.status] : "default"}
                        >
                          <p>{example?.name}</p>
                          {example && example.status === "PUBLISHED" && (
                            <LinkButton
                              styleOverrides={{
                                text: {
                                  fontSize: "var(--flo-base-font-size-2)",
                                  fontWeight: "var(--flo-base-font-weight-5)",
                                  lineHeight: "var(--flo-base-line-height-2)",
                                },
                              }}
                              onClick={() => {
                                setStatusChange({
                                  fromStatus: example.status,
                                  toStatus: "ACTIVE",
                                  exampleSetId: example.id,
                                });
                              }}
                            >
                              {t("components.FilePreview.makeActive")}
                            </LinkButton>
                          )}
                        </TableStatusBadge>
                      )}
                    </Styled.FileTab>
                  )}
                  <Styled.FileTabsScrollContainer>
                    {(clearableFileTabs ?? []).map((tab) => {
                      return (
                        <Styled.FileTab
                          key={tab.id}
                          active={tab.id === activeFileId}
                          onClick={() => {
                            onFileTabClick?.(tab.id);
                          }}
                          title={tab.displayName}
                          hasCloseButton={true}
                        >
                          <Text size={4} weight={5} lineHeight={3}>
                            {tab.displayName}
                          </Text>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onFileTabClose?.(tab.id);
                            }}
                          >
                            <Clear
                              style={{
                                width: "18px",
                                height: "18px",
                                flexShrink: 0,
                              }}
                            />
                          </button>
                        </Styled.FileTab>
                      );
                    })}
                  </Styled.FileTabsScrollContainer>
                </Styled.FileTabs>
              )}
            </Styled.FilePreviewHeaderLeft>
            <Tooltip>
              <Tooltip.Trigger>
                <IconButton
                  aria-label={t("components.FilePreview.downloadFile")}
                  size="md"
                  data-tracking-id="runner-download-file-button"
                  disabled={!file}
                  onClick={() => {
                    if (file) downloadFile(file, { fileName: file.name });
                  }}
                >
                  <Download />
                </IconButton>
              </Tooltip.Trigger>
              <Tooltip.Content style={{ zIndex: 9001 }}>
                {t("components.FilePreview.downloadFile")}
              </Tooltip.Content>
            </Tooltip>
          </>
        ) : null}
      </Styled.FilePreviewHeader>
      {!isCurrentlyLoading && file && sheets && (
        <Styled.FilePreview>
          <Styled.GridSection>
            <Styled.GridContainer ref={gridContainerRef}>
              <AgGridReact
                ref={gridRef}
                theme={gridFilePreviewTheme}
                columnDefs={columnDefs}
                rowData={processedRowData}
                domLayout="normal"
                suppressCellFocus={true}
              />
            </Styled.GridContainer>
            <Styled.SheetTabs>
              {Object.keys(sheets).map((tabName) => (
                <Styled.SheetTab
                  key={tabName}
                  active={activeTab === tabName}
                  onClick={() => setActiveTab(tabName)}
                  title={tabName}
                >
                  <Text size={4} weight={5} lineHeight={3}>
                    {tabName}
                  </Text>
                </Styled.SheetTab>
              ))}
            </Styled.SheetTabs>
          </Styled.GridSection>
        </Styled.FilePreview>
      )}
    </Styled.FilePreviewPanel>
  );
};
