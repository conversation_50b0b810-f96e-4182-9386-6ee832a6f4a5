import { Suspense } from "react";
import ArrowBackIos from "@floqastinc/flow-ui_icons/material/ArrowBackIos";
import { Button, Text, Tooltip } from "@floqastinc/flow-ui_core";
import { RunStatus } from "@floqastinc/transform-v3";
import * as Styled from "./RunnerHeader.styles";
import { t } from "@/utils/i18n";
import { Loading } from "@/components/Loading";
import { getRunStatusDisplayProperties } from "@Transform/shared/lib/workflow-runs";

type RunnerHeaderProps = {
  agentName: string;
  runStatus: RunStatus | undefined;
  onActivityLog: () => void;
  onRun: () => void;
  isActivityLogDisabled: boolean;
  isRunDisabled: boolean;
  humanInTheLoop: boolean | undefined;
  workflowStatus: "DRAFT" | "ACTIVE" | "ARCHIVED";
};

const RunnerHeader = ({
  agentName,
  runStatus,
  onActivityLog,
  onRun,
  isActivityLogDisabled,
  isRunDisabled,
  humanInTheLoop,
  workflowStatus,
}: RunnerHeaderProps) => {
  const { badgeColor, statusText } = runStatus ? getRunStatusDisplayProperties(runStatus) : {};

  return (
    <Styled.HeaderWrapper>
      <Suspense fallback={<Loading />}>
        <Styled.BackLink to="/">
          <ArrowBackIos color="action" size={15} />
          <Text weight="6">{t("components.RunnerHeader.allAgents")}</Text>
        </Styled.BackLink>
        <Styled.HeaderContent>
          <Styled.RunInfo>
            <Styled.RunTitle variant={"h2"}>{agentName}</Styled.RunTitle>
            {badgeColor && statusText ? (
              <Styled.RunStatusBadge hasIcon={false} color={badgeColor}>
                {statusText}
              </Styled.RunStatusBadge>
            ) : null}
          </Styled.RunInfo>
          <Styled.RunOptions>
            <Button
              color="dark"
              variant="outlined"
              onClick={onActivityLog}
              disabled={isActivityLogDisabled}
              data-tracking-id="runner-header-activity-log-button"
            >
              {t("components.RunnerHeader.activityLog")}
            </Button>
            {!humanInTheLoop && workflowStatus === "ACTIVE" ? (
              <Tooltip>
                <Tooltip.Trigger>
                  <Button
                    onClick={onRun}
                    disabled={isRunDisabled || !humanInTheLoop}
                    data-tracking-id="runner-header-run-agent-button"
                  >
                    {t("components.RunnerHeader.runAgent")}
                  </Button>
                </Tooltip.Trigger>
                <Tooltip.Content
                  hasArrow
                  style={{
                    whiteSpace: "wrap",
                    maxWidth: "150px",
                    wordBreak: "break-word",
                  }}
                >
                  {t("components.RunnerHeader.activeAgentsRequire")}
                </Tooltip.Content>
              </Tooltip>
            ) : (
              <Button
                onClick={onRun}
                disabled={isRunDisabled}
                data-tracking-id="runner-header-run-agent-button"
              >
                {t("components.RunnerHeader.runAgent")}
              </Button>
            )}
          </Styled.RunOptions>
        </Styled.HeaderContent>
      </Suspense>
    </Styled.HeaderWrapper>
  );
};

RunnerHeader.propTypes = {};

export default RunnerHeader;
