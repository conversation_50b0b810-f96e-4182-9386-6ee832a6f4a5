import { Children, isValidElement } from "react";
import Markdown from "react-markdown";
import { useQuery } from "@tanstack/react-query";
import { Heading, Skeleton, Text } from "@floqastinc/flow-ui_core";
import * as Styled from "./styles";
import { v3, ApiError } from "@/services/v3";
import { getExamplesQuery } from "@BuilderV3/api/examples";
import { t } from "@/utils/i18n";

export type SubStep = { heading: string; details: string };

const SkeletonStepDetails = () =>
  [1, 2, 3, 4, 5].map((i) => (
    <Styled.DetailStep key={i}>
      <Styled.DetailStepName>
        <Skeleton lines={1} width="50%" />
      </Styled.DetailStepName>
      <Styled.DetailStepDescription>
        <Skeleton lines={4} width="100%" />
      </Styled.DetailStepDescription>
    </Styled.DetailStep>
  ));

const Li = ({
  children,
  ...rest
}: React.DetailedHTMLProps<React.LiHTMLAttributes<HTMLLIElement>, HTMLLIElement>) => {
  type ParagraphProps = { children: React.ReactNode };
  const children_: React.ReactNode[] = [];
  Children.forEach(children, (child) => {
    if (isValidElement(child) && child.type === "p") {
      children_.push((child as React.ReactElement<ParagraphProps>).props.children);
    } else {
      children_.push(child);
    }
  });
  return <li {...rest}>{children_}</li>;
};

const DetailStepList = ({ steps }: { steps: SubStep[] }) => (
  <>
    {steps.map((subStep, i) => (
      <Styled.DetailStep key={subStep.heading}>
        <Styled.DetailStepName>
          <Heading variant="h5" weight="medium">
            {t("components.DetailSteps.subStepHeading", {
              index: i + 1,
              heading: subStep.heading,
            })}
          </Heading>
        </Styled.DetailStepName>
        <Styled.DetailStepDescription>
          <Markdown
            components={{
              li({ node: _node, ...rest }) {
                return <Li {...rest} />;
              },
            }}
          >
            {subStep.details}
          </Markdown>
        </Styled.DetailStepDescription>
      </Styled.DetailStep>
    ))}
  </>
);

type DetailStepsProps = {
  workflowId: string;
  taskId: string;
  isOpen: boolean;
};

export const DetailSteps = ({ workflowId, taskId, isOpen }: DetailStepsProps) => {
  const activeExampleSetQuery = useQuery({
    ...getExamplesQuery({ workflowId, taskId }),
    select: (data) => data.find((exampleSet) => exampleSet.status === "ACTIVE"),
    enabled: isOpen,
  });

  const examplesQuery = useQuery({
    ...getExamplesQuery({ workflowId, taskId }),
    enabled: isOpen,
  });

  const getDescriptionQuery = useQuery({
    queryKey: ["taskDescription", workflowId, taskId],
    queryFn: async () => {
      const response = await v3.taskDescriptions.getTaskActiveDescription({ workflowId, taskId });

      if (response.errors.length > 0) {
        throw new ApiError(response.errors);
      }

      if (!response.data)
        throw new Error("Unexpected data error: no task description body returned.");

      return response.data;
    },
    enabled: isOpen && !!activeExampleSetQuery.data,
    retry: false,
  });

  if (examplesQuery.data?.[0]?.status === "DRAFT") {
    return (
      <Styled.DetailStep>
        <Styled.DetailStepName>
          <Heading variant="h5" weight="medium">
            {t("components.DetailSteps.unpublishedStep")}
          </Heading>
        </Styled.DetailStepName>
        <Styled.DetailStepDescription>
          <Text truncate={false}>{t("components.DetailSteps.taskUnpublished")}</Text>
        </Styled.DetailStepDescription>
      </Styled.DetailStep>
    );
  }

  if (activeExampleSetQuery.isPending || getDescriptionQuery.isPending) {
    return <SkeletonStepDetails />;
  }

  return <DetailStepList steps={getDescriptionQuery.data?.description ?? []} />;
};
