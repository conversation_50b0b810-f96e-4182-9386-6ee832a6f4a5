import { <PERSON>Header } from "@floqastinc/flow-ui_core";
import { Fragment, useEffect } from "react";
import { RunStatus, Workflow } from "@floqastinc/transform-v3";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import * as types from "@floqastinc/transform-v3";
import { useNavigate } from "react-router-dom";
import { DetailsSlideout } from "../DetailsSlideout/DetailsSlideout";
import { InputsDropDown } from "./components/InputsDropDown";
import { Step } from "./components/Step";
import * as Styled from "./StepList.styled";
import { ReviewStep } from "./components/ReviewStep/ReviewStep";
import { isTaskRunStep, StepData } from "./types";
import { StepResult } from "./components/StepResults";
import { t } from "@/utils/i18n";
import v3, { ApiError } from "@/services/v3";
import { AGENTS, BUILDER, STEPS, V3 } from "@/constants";
import { useElementTransitionState } from "@/hooks/useElementTransition";
import { useUpdateWorkflow } from "@BuilderV3/routes/workflows/BuilderPage.hooks";
import { DetailSteps } from "@Transform/components/DetailSteps";

interface StepListProps {
  isPreview: boolean;
  runId: string;
  selectedStep: number | null;
  steps: StepData[];
  setSelectedStep: (index: number | null) => void;
  workflowId: string;
  runInputs?: types.WorkflowRunInput[];
  previewInputs?: types.WorkflowInput[];
  workflow: Workflow;
  file: File | null;
  workflowstatus: RunStatus | undefined;
  runBy: string | undefined;
  createdAt: Date | undefined;
  outputValues: {
    value: string;
    name: string;
    stepNumber: number;
    kind:
      | "JEM_EXPORT"
      | "LLM_PROMPT"
      | "LLM_THREAD"
      | "PDF_TO_XLSX"
      | "SCRIPT"
      | "LLM_SCRIPT"
      | "REVIEW"
      | "JEM_TEMPLATE_FETCH"
      | "NO_OP"
      | "FLOLAKE";
  }[];
}

export const StepList = (props: StepListProps) => {
  const {
    isPreview,
    runId,
    steps,
    selectedStep,
    setSelectedStep,
    runInputs,
    previewInputs,
    workflowId,
    workflow,
    file,
    workflowstatus,
    runBy,
    createdAt,
    outputValues,
  } = props;

  const queryClient = useQueryClient();

  const navigate = useNavigate();
  const updateWorkflow = useUpdateWorkflow();
  const handleRunWorkflow = useMutation({
    mutationFn: async () => {
      try {
        const response = await v3.runs.startWorkflowRun({
          workflowRunId: runId,
        });
        if (response.errors.length) {
          throw new Error(t("components.StepList.Errors.badNetworkResponse"));
        }
        return response;
      } catch (error) {
        console.error(t("components.StepList.Errors.errorRunningWorkflow"), error);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["runs"] });
      queryClient.invalidateQueries({ queryKey: ["tasks"] });
      queryClient.invalidateQueries({ queryKey: ["runStatus"] });
      setSelectedStep(null);
    },
    onError: (error) => {
      console.error(t("components.StepList.Errors.requestError"), error);
    },
  });

  const updateStep = useMutation({
    mutationFn: async ({
      taskRunId,
      status,
    }: {
      taskRunId: string;
      status: RunStatus | undefined;
    }) => {
      const { errors } = await v3.runs.updateTaskRun({
        workflowRunId: runId,
        taskRunId,
        run: { status: status ?? "PENDING" },
      });
      if (errors.length) {
        throw new ApiError(errors);
      }
      return handleRunWorkflow.mutate();
    },
    onError: (error) => {
      console.error(t("components.StepList.Errors.requestError"), error);
    },
  });

  const handleRejectClick = async (stepRunId: string) => {
    updateStep.mutate({ taskRunId: stepRunId, status: "REJECTED" });
  };

  const handleResumeClick = async (stepRunId: string) => {
    updateStep.mutate({ taskRunId: stepRunId, status: "COMPLETED" });
  };

  const initialStepDetail = isPreview ? steps[0] : null;
  const [selectedStepDetail, setSelectedStepDetail, { isActive, onTransitionEnd }] =
    useElementTransitionState<{ name: string; id: string }>(initialStepDetail);

  useEffect(() => {
    if (runId) {
      setSelectedStepDetail(null);
    }
  }, [runId, setSelectedStepDetail]);

  return (
    <Styled.StepListContainer>
      {isPreview && (
        <Styled.SectionHeaderContainer>
          <SectionHeader level={4}>
            <Styled.SectionHeaderHeading>
              {t("components.StepList.agentSteps")}
            </Styled.SectionHeaderHeading>
            <SectionHeader.Subtitle>
              {t("components.StepList.followingStepsExecuted")}
            </SectionHeader.Subtitle>
          </SectionHeader>
        </Styled.SectionHeaderContainer>
      )}
      <Styled.StepsContainer>
        <DetailsSlideout
          isOpen={isActive}
          onClose={() => {
            setSelectedStepDetail(null);
          }}
          onTransitionEnd={onTransitionEnd}
          header={
            selectedStepDetail ? (
              <DetailsSlideout.Header
                stepName={selectedStepDetail.name}
                workflowId={workflowId}
                taskId={selectedStepDetail.id}
              />
            ) : null
          }
          steps={
            selectedStepDetail ? (
              <DetailSteps
                workflowId={workflowId}
                taskId={selectedStepDetail.id}
                isOpen={isActive}
              />
            ) : null
          }
        />
        <InputsDropDown runInputs={runInputs} previewInputs={previewInputs} />
        <Styled.Line />
        {steps.map((step, index) => (
          <Fragment key={index}>
            {isTaskRunStep(step) && step.strategy.kind === "REVIEW" ? (
              <ReviewStep
                name={`${index + 1}. ${step.name}`}
                isSelected={selectedStep === index}
                status={isTaskRunStep(step) ? step.status : undefined}
                isFinalStep={index === steps.length - 1}
                onClick={() => setSelectedStep(index)}
                onRejectClick={() => handleRejectClick(step.taskRunId)}
                onApproveClick={() => handleResumeClick(step.taskRunId)}
              />
            ) : (
              <>
                <Step
                  name={`${index + 1}. ${step.name}`}
                  stepRunId={isTaskRunStep(step) ? step.taskRunId : undefined}
                  status={isTaskRunStep(step) ? step.status : undefined}
                  integrations={step.integrations ?? []}
                  isSelected={selectedStep === index}
                  isPreview={isPreview}
                  strategy={step.strategy}
                  onClick={() => {
                    setSelectedStep(index);
                    // TODO: same issue continued from RunnerPage wrt cutting
                    // down complexity of split between Task and TaskRun
                    // in Preview/running state
                    if (isPreview) {
                      setSelectedStepDetail({
                        name: step.name,
                        id: step.id,
                      });
                    }
                  }}
                  onEditClick={() => {
                    // If the agent is active andd does not have a review step, set it to Draft before editing
                    if (workflow.status === "ACTIVE" && !workflow.humanInTheLoop) {
                      workflow.status = "DRAFT";
                      updateWorkflow.mutateAsync(workflow);
                    }
                    navigate(`/${BUILDER}/${V3}/${AGENTS}/${workflowId}/${STEPS}/${step.id}/`);
                  }}
                  onInfoClick={() => {
                    // Because the step details will open, we should set the current
                    // step to this one to prevent confusion over which step details
                    // are being shown.
                    setSelectedStep(index);
                    setSelectedStepDetail({
                      name: step.name,
                      id: step.id,
                    });
                  }}
                />
              </>
            )}
            {index < steps.length - 1 && <Styled.Line />}
          </Fragment>
        ))}
        {!isPreview && workflowstatus !== "RUNNING" && workflowstatus && file ? (
          <>
            <Styled.Line />
            <StepResult
              file={file}
              workflowstatus={workflowstatus}
              runBy={runBy}
              createdAt={createdAt}
              outputValues={outputValues}
              data-testId="runner-stepList-result"
            />
          </>
        ) : null}
      </Styled.StepsContainer>
    </Styled.StepListContainer>
  );
};
