import { useNavigate, useParams } from "react-router-dom";
import {
  useSuspenseQuery,
  useQuery,
  useMutation,
  useQueries,
  UseQueryOptions,
} from "@tanstack/react-query";
import { useEffect, useState } from "react";
import ChevronLeft from "@floqastinc/flow-ui_icons/material/ChevronLeft";
import ChevronRight from "@floqastinc/flow-ui_icons/material/ChevronRight";
import { NewWorkflowRunInput, ValueResponseResponse, RunStatus } from "@floqastinc/transform-v3";
import { match } from "ts-pattern";
import * as Styled from "./RunnerPage.styled";
import RunnerHeader from "./ui/RunnerHeader/RunnerHeader";
import { StepList } from "./ui/StepsList/StepList";
import { FilePreview } from "./ui/FilePreview";
import { RunInputsWizard } from "./ui/RunInputsWizard";
import { StepData, TaskRunStep } from "./ui/StepsList/types";
import { t } from "@/utils/i18n";
import { getWorkflowQuery } from "@BuilderV3/api/workflows";
import { getRunStatusQuery, getRunInputs } from "@BuilderV3/api/runs";
import { getWorkflowTasksQuery } from "@BuilderV3/api/tasks";
import { v3 } from "@/services/v3";
import { queryClient } from "@/components";
import { AGENTS, RUNNER, RUNS, V3 } from "@/constants";
import { getWorkflowInputsQuery } from "@BuilderV3/api/workflow-inputs";
import { uploadFile } from "@/utils/browser";
import { InputValue } from "@Transform/shared/types";
import { ActivityLogSideDrawer } from "@/apps/Transform/components/ActivityLogSideDrawer/ActivityLogSideDrawer";
import { Loading, OverlayLoading } from "@/components/Loading";
import { useTaskRunPreviewFile } from "@Transform/api/task-run-output";
import { getPrincipalsQuery } from "@BuilderV3/app-components/Audit";

const inputValueToNewWorkflowRunInput = (input: InputValue, workflowInputId: string) =>
  match(input)
    .with({ kind: "FILE" }, (fileInput) => {
      return {
        ...fileInput,
        workflowInputId,
        value: {
          kind: fileInput.kind,
          mimetype: fileInput.value.type,
          name: fileInput.value.name,
        },
      };
    })
    .with({ kind: "DATETIME" }, (dateInput) => {
      return {
        ...dateInput,
        workflowInputId,
        value: { kind: dateInput.kind, value: dateInput.value },
      };
    })
    .with({ kind: "TEXT" }, (textInput) => {
      return {
        ...textInput,
        workflowInputId,
        value: { kind: textInput.kind, value: textInput.value },
      };
    })
    .with({ kind: "NUMBER" }, (numberInput) => {
      return {
        ...numberInput,
        workflowInputId,
        value: { kind: numberInput.kind, value: numberInput.value },
      };
    })
    .exhaustive();

export const RunnerPage = () => {
  const [selectedStep, setSelectedStep] = useState<number | null>(null);
  const [showActivityLog, setShowActivityLog] = useState(false);

  const { agentId = "", runId = "" } = useParams();
  const navigate = useNavigate();
  const searchParams = new URLSearchParams(window.location.search);
  const runSearchParam = searchParams.get("run") === "true";
  const [agentRunUserIds, setAgentRunUserIds] = useState<string[]>([]);
  const [agentRunDate, setAgentRunDate] = useState<Date>();
  const { error: _workflowError, data: workflow } = useSuspenseQuery(getWorkflowQuery(agentId));
  const { data: workflowTasks } = useQuery({
    ...getWorkflowTasksQuery(agentId),
    select: (tasks) => {
      return tasks.map((task) => {
        const integrations: string[] = [];
        if (task.strategy.kind === "FLOLAKE") integrations.push(...task.strategy.sources);

        return {
          ...task,
          integrations,
        };
      });
    },
  });
  const { data: workflowInputs } = useQuery(getWorkflowInputsQuery(agentId));
  const { data: runStatusData, isSuccess: runStatusQueryIsSuccess } = useQuery({
    ...getRunStatusQuery(runId),
    enabled: !!runId,
    select: (data) => {
      return {
        ...data,
        taskRuns: data.taskRuns.map((taskRun) => ({
          ...taskRun,
          // TODO: should taskRun.status ever be undefined?
          status: taskRun.status || "PENDING",
          id: taskRun.taskId,
          taskRunId: taskRun.id,
          strategy: {
            kind: taskRun.strategy.kind,
          },
        })),
      };
    },
  });

  if (!agentRunUserIds.length && runStatusData) {
    setAgentRunUserIds([runStatusData?.workflowRun?.createdBy]);
    setAgentRunDate(runStatusData?.workflowRun?.createdAt);
  }

  const steps: StepData[] =
    (runId && runStatusQueryIsSuccess ? runStatusData?.taskRuns : workflowTasks) ?? [];

  const previewInputs = workflowInputs;

  const runInputsQuery = useQuery({
    ...getRunInputs({ workflowRunId: runId }),
    enabled: !!runId,
  });

  const runInputs = runInputsQuery.data?.data;

  const workflowStatus = runStatusData?.workflowRun?.status;
  const isPreview = !runId;
  const isReview = workflowStatus === "REVIEW";
  const selectedTaskRun = selectedStep !== null ? runStatusData?.taskRuns[selectedStep] : null;

  type QueryKeyArg = { queryKey: [string, string] };
  const fetchTaskRunOutputValue = async ({ queryKey }: QueryKeyArg) => {
    const [, taskRunId] = queryKey;
    const taskRunOutputsRes = await v3.runs.getTaskRunOutputs({
      workflowRunId: runId,
      taskRunId: taskRunId,
    });
    const taskRunOutput = taskRunOutputsRes.data[0];
    const taskRunOutputRes = await v3.runs.getTaskRunOutputValue({
      workflowRunId: runId,
      taskRunId: taskRunId,
      taskRunOutputId: taskRunOutput.id,
    });
    return taskRunOutputRes;
  };
  const fetchOutputValue = async ({ queryKey }: QueryKeyArg) => {
    const [, taskRunId] = queryKey;

    const taskRunOutputValue = await fetchTaskRunOutputValue({
      queryKey: ["", taskRunId],
    });

    return taskRunOutputValue;
  };

  type MidRunStatus = Extract<RunStatus, "FAILED" | "REJECTED" | "REVIEW">;
  const isStoppedMidRunStatus = (status: RunStatus): status is MidRunStatus => {
    return ["FAILED", "REJECTED", "REVIEW"].includes(status);
  };

  const getPreviewTask = (taskRuns: TaskRunStep[] | undefined): TaskRunStep | undefined => {
    if (!taskRuns) return undefined;

    let task: TaskRunStep | undefined;
    if (selectedTaskRun) {
      task = selectedTaskRun;
    } else if (workflowStatus && isStoppedMidRunStatus(workflowStatus)) {
      task = taskRuns.find((task) => task.status && isStoppedMidRunStatus(task.status));
    } else if (workflowStatus === "COMPLETED") {
      task = taskRuns.at(-1);
    }
    return task;
  };
  const previewTask = getPreviewTask(runStatusData?.taskRuns);
  const { data: fileData, isPending: isFileLoading } = useTaskRunPreviewFile(
    runId,
    previewTask?.taskRunId || "",
    `${workflow?.name}${previewTask?.name ? "_" + previewTask.name : "file"}`,
  );
  const previewName = previewTask
    ? `${steps.findIndex((step) => step.id === previewTask.id) + 1}. ${previewTask.name}`
    : "";
  const jemExportTasksAndLastTask = runStatusData?.taskRuns
    .map((taskRun, i) => {
      return { ...taskRun, stepNumber: i + 1 };
    })
    .filter(
      (taskRun, index) =>
        taskRun.strategy.kind === "JEM_EXPORT" || index === runStatusData?.taskRuns?.length - 1,
    );

  const outputValuesQuery = useQueries({
    queries:
      jemExportTasksAndLastTask?.map((task, i) => {
        return {
          queryKey: [i, task?.taskRunId || ""],
          queryFn: (): Promise<ValueResponseResponse> =>
            fetchOutputValue({ queryKey: ["", task?.taskRunId] }),
          select: (data) => {
            if (!data || data.errors?.length) {
              // TODO: Handle errors for any JEM export links. Right now we filter out undefined values so there is no link to render.
              return undefined;
            }
            return {
              data: {
                value: data.data?.value ?? "",
                name: task.name,
                kind: task.strategy.kind,
                stepNumber: task.stepNumber,
              },
            };
          },
          staleTime: 1000 * 30,
          gcTime: 1000 * 60,
          enabled:
            !!previewTask?.id &&
            (workflowStatus === "COMPLETED" ||
              workflowStatus === "FAILED" ||
              workflowStatus === "REJECTED" ||
              isReview),
        } satisfies UseQueryOptions<
          ValueResponseResponse,
          unknown,
          { data: { value?: string; name: string } } | undefined, // <== Allow undefined
          readonly unknown[]
        >;
      }) ?? [],
  });

  const outputValues = outputValuesQuery
    .map((query) => {
      if (!query.data) return undefined;
      return {
        value: query.data?.data?.value ?? "",
        name: query.data?.data?.name ?? "",
        kind: query.data?.data?.kind ?? "",
        stepNumber: query.data?.data?.stepNumber ?? 0,
      };
    })
    .filter(
      (
        item,
      ): item is {
        value: string;
        name: string;
        stepNumber: number;
        kind:
          | "JEM_EXPORT"
          | "LLM_PROMPT"
          | "LLM_THREAD"
          | "PDF_TO_XLSX"
          | "SCRIPT"
          | "LLM_SCRIPT"
          | "REVIEW"
          | "JEM_TEMPLATE_FETCH"
          | "NO_OP"
          | "FLOLAKE";
      } => item !== undefined,
    );

  const [inputValuesByInputId, setInputValuesByInputId] = useState<{
    [id: string]: InputValue;
  }>({});

  const [isInputsWizardOpen, setIsInputsWizardOpen] = useState(
    Boolean(runSearchParam && workflowInputs && workflowInputs.length),
  );

  const [isCollapsed, setIsCollapsed] = useState(false);

  // Show inputs wizard if the run is pending, there are inputs, and there are no run inputs
  // This is needed when navigating directly to /runner/v3/agents/{agentId}/runs/{runId} from the Checklists page
  useEffect(() => {
    const hasIncompleteRunInputs =
      runInputsQuery.data?.data?.some((input) => input.value == undefined) ?? false;
    if (hasIncompleteRunInputs) {
      setIsInputsWizardOpen(true);
    }
  }, [runInputsQuery.data]);

  const updateExistingRunInputsMutation = useMutation({
    mutationFn: async ({
      runId,
      inputsById,
      currentRunInputs,
    }: {
      runId: string;
      inputsById: { [id: string]: InputValue };
      currentRunInputs: typeof runInputs;
    }) => {
      const promises = Object.entries(inputsById).map(async ([inputId, input]) => {
        const runInput = currentRunInputs?.find((ri) => ri.workflowInputId === inputId);
        if (!runInput) return;

        if (input.type === "FILE" && input.value && input.kind === "FILE") {
          const file = input.value as File;

          const { data, errors } = await v3.runs.updateWorkflowRunInputFile({
            workflowRunId: runId,
            workflowRunInputId: runInput.id,
            body: {
              kind: "FILE",
              mimetype: file.type,
              name: file.name,
            },
          });

          if (errors.length) {
            console.error("error", errors);
            throw new Error(t("components.RunnerPage.Errors.inputError"));
          }

          // Upload the file using the presigned URL
          if (data) {
            await uploadFile(file, data.url);
          }
        } else if (input.type === "TEXT" && input.kind === "TEXT") {
          const { errors } = await v3.runs.setWorkflowRunTextInput({
            workflowRunId: runId,
            workflowRunInputId: runInput.id,
            value: {
              kind: "TEXT",
              value: input.value as string,
            },
          });

          if (errors.length) {
            console.error("error", errors);
            throw new Error(t("components.RunnerPage.Errors.inputError"));
          }
        } else if (input.type === "NUMBER" && input.kind === "NUMBER") {
          const { errors } = await v3.runs.setWorkflowRunNumberInput({
            workflowRunId: runId,
            workflowRunInputId: runInput.id,
            value: {
              kind: "NUMBER",
              value:
                typeof input.value === "string" ? parseFloat(input.value) : (input.value as number),
            },
          });

          if (errors.length) {
            console.error("error", errors);
            throw new Error(t("components.RunnerPage.Errors.inputError"));
          }
        } else if (input.type === "DATETIME" && input.kind === "DATETIME") {
          if (input.value) {
            const { errors } = await v3.runs.setWorkflowRunDatetimeInput({
              workflowRunId: runId,
              workflowRunInputId: runInput.id,
              value: {
                kind: "DATETIME",
                value: input.value as Date,
              },
            });

            if (errors.length) {
              console.error("error", errors);
              throw new Error(t("components.RunnerPage.Errors.inputError"));
            }
          }
        }
      });

      await Promise.all(promises);

      return runId;
    },
    onSuccess: (runId) => {
      queryClient.invalidateQueries({
        queryKey: ["runStatus", runId],
      });
      queryClient.invalidateQueries({
        queryKey: [`/runs/${runId}/inputs`],
      });
      startWorkflowMutation.mutate(runId);
    },
    onError: (error) => {
      console.error("Error updating run inputs:", error);
    },
  });

  const principalsQuery = useQuery({
    ...getPrincipalsQuery(agentRunUserIds),
    enabled: agentRunUserIds.length > 0,
    select: (data) => {
      // Transform into a { [id]: name } format to allow for easier
      //  melding into the agentRuns data for display in the UI
      return data.reduce(
        (acc, principal) => {
          const displayName = match(principal)
            .with({ kind: "API_KEY" }, ({ friendlyName }) => friendlyName ?? "API Key")
            .otherwise(({ name }) => name);

          return {
            ...acc,
            [principal.id]: displayName,
          };
        },
        {} as Record<string, string>,
      );
    },
  });

  const createRunMutation = useMutation({
    mutationFn: async (paramWorkflowId: string) => {
      if (!paramWorkflowId) {
        throw new Error(t("components.RunnerPage.Errors.noWorkflowID"));
      }

      const filesByFileInputId: Record<string, File> = {};
      let inputs: NewWorkflowRunInput[] = [];
      if (workflowInputs && workflowInputs.length > 0) {
        // TODO: typecheck
        inputs = Object.entries(inputValuesByInputId).map(([workflowInputId, input]) => {
          if (input.type === "FILE") {
            filesByFileInputId[input.id] = input.value as File;
          }

          return inputValueToNewWorkflowRunInput(input, workflowInputId);
        });
      }

      const runType = searchParams.get("runType") === "TEST" ? "TEST" : "REGULAR";
      const { data: workflowRunData, errors } = await v3.runs.createWorkflowRun({
        run: { inputs: inputs.length > 0 ? inputs : undefined, runType: runType },
        workflowId: paramWorkflowId,
      });
      if (errors.length) {
        console.error("error", errors);
        throw new Error(t("components.RunnerPage.Errors.badNetworkResponse"));
      }
      if (!workflowRunData) {
        console.error("error", t("components.RunnerPage.Errors.noDataReturned"));
        throw new Error(t("components.RunnerPage.Errors.noDataFromWorkflowRun"));
      }

      const { filePresignedUrls, workflowRun } = workflowRunData;

      // If file inputs were included in the agent run creation call, then
      //  presignedUrls will be returned by the API for the frontend to use
      //  to upload these files to S3 before starting the run.
      if (filePresignedUrls) {
        const uploadPromises = filePresignedUrls.map(async ({ workflowInputId, presignedUrl }) => {
          const input = inputs.find((input) => input.workflowInputId === workflowInputId);
          if (!input) {
            throw new Error(
              t("components.RunnerPage.Errors.noInputFoundForID") + `${workflowInputId}`,
            );
          }
          const file = filesByFileInputId[workflowInputId];
          if (!file) {
            throw new Error(
              t("components.RunnerPage.Errors.noFileFoundForID") + `${workflowInputId}`,
            );
          }
          await uploadFile(file, presignedUrl.url);
        });
        await Promise.all(uploadPromises);
      }

      return workflowRun;
    },
    onSuccess: (workflowRunData) => {
      if (!workflowRunData) {
        throw new Error(t("components.RunnerPage.Errors.noRunData"));
      }
      setAgentRunUserIds([workflowRunData?.createdBy]);
      setAgentRunDate(workflowRunData?.createdAt);
      setSelectedStep(null);
      startWorkflowMutation.mutate(workflowRunData.id);
      queryClient.invalidateQueries({
        queryKey: ["runs", workflowRunData.workflowId],
      });
      navigate(
        `/${RUNNER}/${V3}/${AGENTS}/${workflowRunData.workflowId}/${RUNS}/${workflowRunData.id}`,
        { replace: true },
      );
    },
    onError: (error) => {
      console.error("error", error);
    },
  });

  const startWorkflowMutation = useMutation({
    mutationFn: async (workflowRunId: string) => {
      if (!workflowRunId) {
        throw new Error(t("components.RunnerPage.Errors.noWorkflowID"));
      }
      const { errors } = await v3.runs.startWorkflowRun({ workflowRunId });
      if (errors.length) {
        console.error("error", errors);
        throw new Error(t("components.RunnerPage.Errors.badNetworkResponse"));
      }

      return workflowRunId;
    },
    onSuccess: (workflowRunId) => {
      queryClient.invalidateQueries({ queryKey: ["runs"] });
      queryClient.invalidateQueries({ queryKey: ["tasks"] });
      queryClient.invalidateQueries({ queryKey: ["runStatus", workflowRunId] });
    },
    onError: (error) => {
      console.error("error", error);
    },
  });

  function onRun() {
    return workflowInputs && workflowInputs.length > 0
      ? setIsInputsWizardOpen(true)
      : createRunMutation.mutate(agentId);
  }

  useEffect(() => {
    if (workflowInputs && runSearchParam) onRun();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [workflowInputs, runSearchParam]);

  const isRunning =
    workflowStatus && !isStoppedMidRunStatus(workflowStatus) && workflowStatus !== "COMPLETED";

  return (
    <Styled.Page>
      {startWorkflowMutation.isPending || createRunMutation.isPending ? <OverlayLoading /> : null}
      <RunnerHeader
        agentName={workflow?.name || ""}
        runStatus={workflowStatus}
        onActivityLog={() => {
          setShowActivityLog(!showActivityLog);
        }}
        isActivityLogDisabled={false}
        onRun={onRun}
        isRunDisabled={createRunMutation.isPending}
        humanInTheLoop={workflow?.humanInTheLoop}
        workflowStatus={workflow?.status}
      />
      <Styled.RunnerBody>
        {!steps?.length && runStatusQueryIsSuccess ? (
          <Styled.AlertBanner>
            <strong>{t("components.RunnerPage.agentHasNoSteps")}</strong>{" "}
            {t("components.RunnerPage.agentHasNoSteps2")}
          </Styled.AlertBanner>
        ) : (
          <>
            {isPreview && (
              <Styled.AlertBanner>
                <strong>{t("components.RunnerPage.agentPreview")}</strong>
                {t("components.RunnerPage.agentPreview2")}
              </Styled.AlertBanner>
            )}
            <Styled.RunnerBodyContent>
              {workflowInputs ? (
                <RunInputsWizard
                  inputs={workflowInputs}
                  isOpen={isInputsWizardOpen}
                  onOpenChange={setIsInputsWizardOpen}
                  onCompletion={(inputsById) => {
                    setInputValuesByInputId(inputsById);
                    // Check if we're adding to an existing run with undefined inputs
                    const hasIncompleteRunInputs =
                      runInputsQuery.data?.data?.some((input) => input.value == undefined) ?? false;
                    if (runId && hasIncompleteRunInputs) {
                      updateExistingRunInputsMutation.mutate({
                        runId,
                        inputsById,
                        currentRunInputs: runInputsQuery.data?.data,
                      });
                    } else {
                      // Create a new run as before
                      createRunMutation.mutate(agentId);
                    }
                  }}
                />
              ) : null}
              <Styled.StepListContainer $isPreview={isPreview} $isCollapsed={isCollapsed}>
                <StepList
                  runInputs={runInputs}
                  previewInputs={previewInputs}
                  isPreview={isPreview}
                  runId={runId}
                  steps={steps}
                  selectedStep={selectedStep}
                  setSelectedStep={setSelectedStep}
                  workflowId={workflow.id}
                  file={fileData?.file || null}
                  workflow={workflow}
                  workflowstatus={workflowStatus}
                  runBy={principalsQuery.data?.[agentRunUserIds[0]]}
                  createdAt={agentRunDate}
                  outputValues={outputValues}
                />
                {!isPreview && (
                  <Styled.CollapseButton onClick={() => setIsCollapsed(!isCollapsed)}>
                    {isCollapsed ? <ChevronRight /> : <ChevronLeft />}
                  </Styled.CollapseButton>
                )}
              </Styled.StepListContainer>
              {!isPreview && (
                <Styled.FilePreviewContainer>
                  {isRunning && !fileData?.file ? (
                    <Loading text="Agent running..." />
                  ) : (
                    <FilePreview
                      previewName={previewName}
                      file={fileData?.file || null}
                      isLoading={isFileLoading}
                      mode="runner"
                    />
                  )}
                </Styled.FilePreviewContainer>
              )}
              <ActivityLogSideDrawer
                show={showActivityLog}
                agentId={agentId}
                agentName={workflow?.name || ""}
                onClose={() => setShowActivityLog(false)}
              />
            </Styled.RunnerBodyContent>
          </>
        )}
      </Styled.RunnerBody>
    </Styled.Page>
  );
};
