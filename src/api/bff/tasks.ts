import { featureFlags } from "@/components/FeatureFlag";
import v3, { ApiError } from "@/services/v3";
import * as apiSdk from "@floqastinc/transform-v3";
import { Task, TaskInput } from "@floqastinc/transform-v3";

type RevertTaskArgs = {
  workflowId: string;
  taskId: string;
  tasks: apiSdk.Task[];
};
export const revertTaskMutation = async ({ workflowId, taskId, tasks }: RevertTaskArgs) => {
  const taskToRevertToIndex = tasks.findIndex((task) => task.id === taskId);

  if (taskToRevertToIndex === -1) {
    throw new Error("Task to revert to not found");
  }

  const tasksToRemove = tasks.slice(taskToRevertToIndex + 1).map((task) => task.id);
  await Promise.all(tasksToRemove.map((taskId) => v3.tasks.deleteTask({ workflowId, taskId })));

  // point workflow output source to last task after reverting
  const revertedTasks = tasks.slice(0, taskToRevertToIndex + 1);
  const lastTask = revertedTasks[revertedTasks.length - 1];
  const { data: taskOutputsRes } = await v3.taskOutputs.getTaskOutputs({
    taskId: lastTask.id,
    workflowId,
  });
  const { data: workflowOutputsRes } = await v3.workflowOutputs.getWorkflowOutputs({ workflowId });
  await v3.workflowOutputs.updateWorkflowOutput({
    workflowId,
    workflowOutputId: workflowOutputsRes[0].id,
    output: {
      source: { taskId: lastTask.id, taskOutputId: taskOutputsRes[0].id },
    },
  });
};

type DeleteTaskArgs = {
  workflowId: string;
  taskId: string;
  tasks: apiSdk.Task[];
};
export const deleteTaskMutation = async ({ workflowId, taskId, tasks }: DeleteTaskArgs) => {
  const taskToDeleteIndex = tasks.findIndex((task) => task.id === taskId);
  if (taskToDeleteIndex === -1) {
    throw new Error("Task to delete not found");
  }
  const tasksToRemove = tasks.slice(taskToDeleteIndex).map((task) => task.id);
  await Promise.all(tasksToRemove.map((taskId) => v3.tasks.deleteTask({ workflowId, taskId })));

  // point workflow output source to last task after deleting
  const lastTask = tasks[taskToDeleteIndex - 1];
  const { data: taskOutputsRes } = await v3.taskOutputs.getTaskOutputs({
    taskId: lastTask.id,
    workflowId,
  });
  const { data: workflowOutputsRes } = await v3.workflowOutputs.getWorkflowOutputs({ workflowId });
  await v3.workflowOutputs.updateWorkflowOutput({
    workflowId,
    workflowOutputId: workflowOutputsRes[0].id,
    output: {
      source: { taskId: lastTask.id, taskOutputId: taskOutputsRes[0].id },
    },
  });

  return lastTask;
};

const createTaskInputs = async ({
  workflowId,
  task,
  previousTaskId,
  prevTaskOutputs,
}: {
  workflowId: string;
  task: Task;
  previousTaskId: string;
  prevTaskOutputs: apiSdk.TaskOutput[];
}) => {
  if (task.strategy.kind === "FLOLAKE" || task.strategy.kind === "JEM_TEMPLATE_FETCH") {
    return [];
  }
  const newTaskInputs = [];
  for (const prevTaskOutput of prevTaskOutputs) {
    const source: apiSdk.Source = {
      taskId: previousTaskId,
      taskOutputId: prevTaskOutput.id,
    };
    const createTaskInputQuery = await v3.taskInputs.createTaskInput({
      workflowId,
      taskId: task.id,
      input: {
        name: prevTaskOutput.name,
        type: prevTaskOutput.type,
        source,
      },
    });
    if (createTaskInputQuery.errors.length) {
      throw new ApiError(createTaskInputQuery.errors);
    }
    if (!createTaskInputQuery.data) {
      throw new Error("Unexpected data error: Failed to create task input");
    }

    newTaskInputs.push(createTaskInputQuery.data);
  }
  return newTaskInputs;
};

const createTaskOutputs = async ({ workflowId, task }: { workflowId: string; task: Task }) => {
  let type: apiSdk.DataType = "FILE";
  switch (task.strategy.kind) {
    case "FLOLAKE":
    case "JEM_TEMPLATE_FETCH":
    case "LLM_THREAD":
    case "SCRIPT":
    case "REVIEW":
      type = "FILE";
      break;
    case "JEM_EXPORT":
      type = "TEXT";
      break;
    default:
      throw new Error("Must specify output type for task strategy kind");
  }
  const { data: taskOutput, errors: createTaskErrors } = await v3.taskOutputs.createTaskOutput({
    workflowId,
    taskId: task.id,
    output: {
      name: `${task.name} Output`,
      type,
    },
  });

  if (createTaskErrors.length) {
    throw new ApiError(createTaskErrors);
  }
  if (!taskOutput) {
    throw new Error("Unexpected data error: Failed to create task output");
  }
  return taskOutput;
};

export type CreateTaskParams = {
  workflowId: string;
  task: apiSdk.NewTask;
  previousTaskId?: string;
};
// TODO: Deer lord this is a lot of duct tape. This would
//  be better suited to be in a BFF aggregate endpoint at some point
//  since the logic is rather involved with regards to stitching together
//  resources and has no atomicity.
/**
 * Aggregate API call for creating a Task and piping previous task
 *  outputs into the inputs for the given task. Also creates the initial
 *  exampleSet.
 */
export const createTask = async ({
  workflowId,
  task,
  previousTaskId,
}: CreateTaskParams): Promise<{
  task: apiSdk.Task;
  taskInputs: apiSdk.TaskInput[] | undefined;
  taskOutput: apiSdk.TaskOutput;
  example: apiSdk.ExampleSet;
}> => {
  const { data: createdTask, errors } = await v3.tasks.createTask({
    workflowId,
    task,
  });

  if (errors.length) {
    throw new ApiError(errors);
  }
  if (!createdTask) {
    throw new Error("Unexpected data error: Failed to create task");
  }

  // create new task input using previous task output
  let taskInputs: TaskInput[] | undefined;
  if (previousTaskId) {
    const { data: taskOutputs, errors } = await v3.taskOutputs.getTaskOutputs({
      workflowId,
      taskId: previousTaskId,
    });
    if (errors.length) {
      throw new ApiError(errors);
    }
    if (!taskOutputs) {
      throw new Error("Unexpected data error: Failed to get previous task outputs");
    }

    taskInputs = await createTaskInputs({
      workflowId,
      task: createdTask,
      previousTaskId,
      prevTaskOutputs: taskOutputs,
    });
  }

  const taskOutput = await createTaskOutputs({ workflowId, task: createdTask });

  // point workflow output source to new task output
  const { data: workflowOutputsRes } = await v3.workflowOutputs.getWorkflowOutputs({ workflowId });
  await v3.workflowOutputs.updateWorkflowOutput({
    workflowId,
    workflowOutputId: workflowOutputsRes[0].id,
    output: {
      source: { taskId: createdTask.id, taskOutputId: taskOutput.id },
    },
  });

  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const exampleResponse = await v3.examples.createExample({
    workflowId,
    taskId: createdTask.id,
    example: {
      status: "DRAFT",
      timezone,
    },
  });

  if (exampleResponse.errors?.length) {
    throw new ApiError(exampleResponse.errors);
  }
  if (!exampleResponse.data) {
    throw new Error("Unexpected data error: Failed to create example");
  }
  const example = exampleResponse.data;

  if (createdTask.strategy.kind === "JEM_TEMPLATE_FETCH") {
    try {
      const jemTemplate = await v3.jemTemplate.getJemTemplate({
        workflowId,
        taskId: createdTask.id,
        exampleSetId: example.id,
      });
      if (jemTemplate.errors?.length) {
        throw new ApiError(jemTemplate.errors);
      }
    } catch (error) {
      console.error("Error fetching JEM Template:", error);
    }
  }
  // if transform-file-sample is undefined or true, create a file sample
  if (featureFlags.get("transform-file-sample") !== false) {
    try {
      const exampleInputs = await v3.exampleInputs.getExampleInputs({
        workflowId,
        taskId: createdTask.id,
        exampleSetId: example.id,
      });
      if (exampleInputs.errors?.length) {
        throw new ApiError(exampleInputs.errors);
      }
      await Promise.all(
        exampleInputs.data
          .filter((input) => input.value?.kind === "FILE")
          .map((exampleInput) => {
            return v3.fileSamples.createFileSample({
              workflowId,
              taskId: createdTask.id,
              exampleSetId: example.id,
              exampleInputId: exampleInput.id,
            });
          }),
      );
    } catch (e) {
      console.error(e);
    }
  }

  return { task: createdTask, taskInputs, taskOutput, example };
};
