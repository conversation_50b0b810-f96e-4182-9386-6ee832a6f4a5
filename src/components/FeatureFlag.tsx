import { useEffect, useState } from "react";
import { I<PERSON><PERSON><PERSON><PERSON>, DropdownPanel, Button, Input, Toggle, Text } from "@floqastinc/flow-ui_core";
import Flag from "@floqastinc/flow-ui_icons/material/Flag";
import Close from "@floqastinc/flow-ui_icons/material/Close";
import { styled } from "styled-components";
import { match, P } from "ts-pattern";
import { AutocompleteStringUnion } from "@/types";
import { t } from "@/utils/i18n";

const useLocalStorage = (key: string, initialValue: any) => {
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      const isEmptyObject = item === "{}" || item === "null" || item === "undefined";
      return item && !isEmptyObject ? JSON.parse(item) : initialValue;
    } catch (e) {
      console.error(e);
      return initialValue;
    }
  });
  const setValue = (value: any) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (e) {
      console.error(e);
    }
  };
  return [storedValue, setValue];
};

// Feature Flag Keys
/* eslint-disable react-refresh/only-export-components */

export const DEFAULT_FLAGS = {
  "inputs-ux": false,
  "file-context-enabled": false,
  "transform-file-sample": true,
  "realtime-messages": true,
  "legacy-builder": false,
  "enable-workflow-versioning": false,
  "enable-builder-test-runs": false,
  "enable-builder-page-settings": false,
  "enable-canvas-view": false,
  "enable-builder-input-editing": false,
  "enable-activate-test": false,
  "enable-experiments": false,
  "enable-global-inputs": false,
  "transform-advanced-step-manager": false,
  "enable-pre-create-agent-step": false,
};

type FlagKey = AutocompleteStringUnion<keyof typeof DEFAULT_FLAGS>;
export const featureFlags = {
  set(flag: FlagKey, value: boolean) {
    // Explicitly typing this loosely since we want to be able to set
    //  arbitrary flags if necessary.
    const flags: Record<string, boolean> = { ...DEFAULT_FLAGS };
    flags[flag] = value;
    localStorage.setItem("transform-feature-flags", JSON.stringify(flags));
  },
  get(flag: FlagKey): boolean {
    const flags = JSON.parse(localStorage.getItem("transform-feature-flags") || "{}");
    if (flags[flag] === undefined) {
      return DEFAULT_FLAGS[flag as keyof typeof DEFAULT_FLAGS];
    }
    return flags[flag];
  },
  remove(flag: FlagKey) {
    const flags = JSON.parse(localStorage.getItem("transform-feature-flags") || "{}");
    delete flags[flag];
    localStorage.setItem("transform-feature-flags", JSON.stringify(flags));
  },
};

export const useFeatureFlags = () => {
  const [flags, setFlags] = useLocalStorage("transform-feature-flags", DEFAULT_FLAGS);

  const setFlag = (flag: FlagKey, value: boolean) => {
    setFlags({ ...flags, [flag]: value });
  };

  const getFlag = (flag: FlagKey, useFalseAsFallback: boolean = true) => {
    return useFalseAsFallback ? (flags[flag] ?? false) : flags[flag];
  };

  const removeFlag = (flag: FlagKey) => {
    const newFlags = { ...flags };
    delete newFlags[flag];
    setFlags(newFlags);
  };

  return { flags, getFlag, setFlag, removeFlag };
};
/* eslint-enable react-refresh/only-export-components */

const BottomRightDiv = styled.div`
  position: fixed;
  z-index: 9999;
  bottom: 10px;
  right: 30px;
  background: transparent;
`;

const OptionDiv = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
`;

const ScrollableDiv = styled.div`
  overflow: auto;
  height: 50vh;
`;

export const FeatureFlagDevTools = () => {
  const { flags, getFlag, setFlag, removeFlag } = useFeatureFlags();
  const isDev = process.env.NODE_ENV === "development";
  const [featureFlagsEnabled] = useLocalStorage("transform-feature-flags-enabled", false);

  // Initialize default flags if they don't already exist in local-storage
  useEffect(() => {
    for (const flag in DEFAULT_FLAGS) {
      if (flags[flag] === undefined) {
        setFlag(flag, DEFAULT_FLAGS[flag as keyof typeof DEFAULT_FLAGS]);
      }
    }
  }, []);

  // const role = usePrincipal().role;
  // TODO: obviously get rid of this before hitting prod...
  const role = "OMNI";

  const [input, setInput] = useState("");
  const [searchText, setSearchText] = useState("");

  // Feature flags should be automatically enabled for dev
  //  and optionally manually toggleable in local-storage
  //  for all other environments.
  if (!featureFlagsEnabled) return null;
  if (!isDev && role !== "OMNI") return null;

  return (
    <BottomRightDiv>
      <DropdownPanel
        onFilterValueChange={(filterValue: string) => {
          setSearchText(filterValue);
        }}
      >
        <DropdownPanel.Trigger>
          <IconButton isActive onClick={() => {}}>
            <Flag />
          </IconButton>
        </DropdownPanel.Trigger>
        <DropdownPanel.Content>
          <ScrollableDiv>
            {flags &&
              Object.keys(flags).map(
                (flag) =>
                  flag.includes(searchText) && (
                    <OptionDiv key={flag}>
                      <Text>{flag}</Text>
                      <Toggle
                        checked={getFlag(flag)}
                        onChange={(value: boolean) => {
                          setFlag(flag, value);
                        }}
                      />
                      <IconButton
                        onClick={() => {
                          removeFlag(flag);
                        }}
                      >
                        <Close />
                      </IconButton>
                    </OptionDiv>
                  ),
              )}
          </ScrollableDiv>
          <div style={{ padding: "16px" }}>
            <Input
              value={input}
              onChange={(value: string) => {
                setInput(value);
              }}
              placeholder={t("components.Builder.addFlag")}
            >
              <Input.AddonRight>
                <Button
                  onClick={() => {
                    setFlag(input, false);
                  }}
                >
                  {t("components.Builder.add")}
                </Button>
              </Input.AddonRight>
            </Input>
          </div>
        </DropdownPanel.Content>
      </DropdownPanel>
    </BottomRightDiv>
  );
};

type FeatureFlagProps =
  | {
      flag: AutocompleteStringUnion<keyof typeof DEFAULT_FLAGS>;
      children: React.ReactNode;
    }
  | {
      flag: AutocompleteStringUnion<keyof typeof DEFAULT_FLAGS>;
      render: (flagValue: boolean) => React.ReactNode;
    };
/**
 * Feature flag component that either conditionally renders children
 *  or uses a custom `render` function to render any fallback components.
 *
 * @example
 * ```tsx
 * const MyComponent = () => (
 *   <ClientFeatureFlag flag="my-flag">
 *     <div>renders if my-flag is true or undefined</div>
 *   </ClientFeatureFlag>
 * );
 *
 * const MyComponentWithFallback = () => (
 *   <ClientFeatureFlag
 *     flag="my-flag"
 *     render={(isEnabled) => isEnabled ? (
 *       <div>shows if the flag is enabled</div>
 *     ) : (
 *       <div>fallback component if flag is disabled</div>
 *     )}
 *   />
 * );
 * ```
 */
export const ClientFeatureFlag = (props: FeatureFlagProps) => {
  const { getFlag } = useFeatureFlags();
  const [featureFlagsEnabled] = useLocalStorage("transform-feature-flags-enabled", false);

  const flagValue: boolean = getFlag(props.flag, false);

  // If flags aren't enabled, don't render any flagged components.
  if (!featureFlagsEnabled || !flagValue)
    return match(props)
      .with({ render: P.instanceOf(Function) }, (props_) => props_.render(false))
      .otherwise(() => null);

  const flaggedComponent = match(props)
    .with({ render: P.instanceOf(Function) }, (props_) => props_.render(true))
    .otherwise((props_) => props_.children);
  return <>{flaggedComponent}</>;
};
