{"components": {"ActivityLog": {"activityLog": "", "ActvityHistoryItem": {"runBy": ""}, "addInputsToRunAgent": "", "agentHasNoSteps": "", "agentHasNoSteps2": "", "agentPreview": "", "agentPreview2": "", "agentSteps": "", "approveRun": "", "enterInput": "", "errorOccurredWhileLoadingRuns": "", "followingStepsExecuted": "", "inputsCount": "", "journalEntries": "", "noRunsFound": "", "rejectRun": "", "resumeRun": "", "runAgent": "", "runBy": "", "stepOfInput": "", "unexpectedError": "", "unsupportedInputType": "", "uploadInput": "", "uploadInputFile": "", "viewErrorDetails": ""}, "Admin": {"actionPermanentDeleteTeam": "", "actionWillMoveUser": "", "actionWillMoveYou": "", "additionalFunctionality": "", "APIKeyCreated": "", "apiKeyDeleted": "", "APImakeRequests": "", "APISwaggerUI": "", "attemptedRuns": "", "backToApp": "", "changeTeam": "", "completedAttempted": "", "completionRate": "", "createdAt": "", "createdNewAPIKey": "", "createKey": "", "deleteAPIKey": "", "deleteTeam": "", "deleteUser": "", "emailedTempPassword": "", "emailSent": "", "failedToFetchScript": "", "failedToSaveScript": "", "filterWorkflows": "", "getStartedInviting": "", "hasBeenMigrated": "", "inviteByEmail": "", "inviteMoreUsers": "", "inviteSent": "", "inviteUser": "", "lastUsed": "", "loadMore": "", "newCreatedBy": "", "newEntityID": "", "noAPIKey": "", "noOneHere": "", "notFound": "", "noWorkflowsFound": "", "organizationSettings": "", "permanentlyDelete": "", "permanentlyDeleteAPIKey": "", "permDeleteUser": "", "pleaseCopySecret": "", "resendEmail": "", "roleUpdated": "", "runDetails": "", "runsByDate": "", "runsByStatus": "", "runStats": "", "scriptSaved": "", "secretAPIKey": "", "secretKey": "", "selectStep": "", "selectTeamMigrate": "", "sentInvitationTo": "", "somethingWentWrong": "", "startAPIKey": "", "switchTeams": "", "taskRunStats": "", "taskRunStatsByDate": "", "taskRunStatsByStrat": "", "teamExternalID": "", "teamName": "", "theWorkflow": "", "totalRuns": "", "typeDelete": "", "unableCreateAPI": "", "unableDeleteAPI": "", "unableDeleteUser": "", "unableTempPassword": "", "unableToInviteUser": "", "unableToMigrateWorkflow": "", "unableToUpdateUserRole": "", "userDeleted": "", "userDeletedMessage": "", "userDeleteUndone": "", "userRoleUpdated": "", "users": "", "viewScripts": "", "workflow": "", "workflowID": "", "workflowMigrated": "", "workflowRunID": "", "workflows:": "", "workflowScripts": "", "workflowStats": ""}, "AgentList": {"agents": "", "connectedData": "", "deleteAgent": "", "editAgent": "", "lastEdited": "", "lastRun": "", "oneHumanStep": "", "searchAgents": "", "showActivityLog": "", "unexpectedFallthrough": ""}, "Builder": {"aboutTo": "", "aboutTo2": "", "aboutToDeleteStep": "", "actionBarEdit": "", "actionBarSave": "", "actions": "", "activateOnly": "", "activateTest": "", "activeExperiments": "", "add": "", "addCustomPrompt": "", "addFlag": "", "addInput": "", "addInputsPrevSteps": "", "addItem": "", "addNewStep": "", "addSection": "", "addStep": "", "agentDescriptionOptional": "", "agentName": "", "agentNamePlaceholder": "", "allFilesUnchanged": "", "analyzingFiles": "", "aPreviousStep": "", "archived": "", "areYouSure": "", "areYouSureRegenerateResponse": "", "autoOpeningNewOutput": "", "builderInput": "", "buildWorkflow": "", "cancel": "", "cannotUndo": "", "changeFrom": "", "changingFrom": "", "chatMSGTextArea": "", "chatWithAI": "", "checkSQLSyntax": "", "confirmAction": "", "copySQL": "", "copySQLErrorMessage": "", "copyUserPrompt": "", "couldTakeAMinute": "", "CreateAgent": {"advancedSetup": "", "experiments": "", "modes": "", "off": "", "on": ""}, "createBlankVersion": "", "createNewWorkflow": "", "createStep": "", "createVersion": "", "currentOutput": "", "dataSources": "", "date": "", "datetime": "", "default": "", "delete": "", "deleteInput": "", "deleteMessage": "", "deleteRun": "", "deleteStep": "", "deleteStepAfter": "", "deleteVersion": "", "deleteWorkflow": "", "description": "", "detailsHere": "", "developmentVer": "", "draft": "", "draftMessage": "", "draftOneOutput": "", "dropFiles": "", "dropFilesSendMsg": "", "edit": "", "editAgent": "", "editMessage": "", "editNotes": "", "editStep": "", "editTaskDescription": "", "editTitle": "", "editWorkflow": "", "enhanceWithAI": "", "enhancingWithAI": "", "ensureInputsFilled": "", "enterADesc": "", "enterATitle": "", "enterDesc": "", "enterDescription": "", "enterSQLQuery": "", "enterTitle": "", "enterValue": "", "entity": "", "Errors": {"agentNameRequired": "", "appSyncAuthenticationValidationInvalid": "", "badNetworkResponse": "", "cantTransformSource": "", "currentTaskNotFound": "", "emptyMessage": "", "emptyPrompt": "", "enterSQLDescription": "", "errorApplyingSchema": "", "errorCreatingContext": "", "errorDuringCleanup": "", "errorExecutingFlolake": "", "errorExecutingWorkflow": "", "errorFetchingEntities": "", "errorFetchingStrat": "", "errorHandlingInputs": "", "errorUpdatingTask": "", "errorUpdatingWorkflow": "", "failedCreateExample": "", "failedCreateInputs": "", "failedCreateOutputs": "", "failedCreateTask": "", "failedCreateTaskInput": "", "failedCreateTaskOutput": "", "failedCreateWorkflow": "", "failedDeleteExample": "", "failedDeleteInputs": "", "failedDeleteOutputs": "", "failedExampleInputs": "", "failedExampleOutputs": "", "failedGetExample": "", "failedGetTask": "", "failedGetTaskInputURI": "", "failedGetTaskOutputs": "", "failedGetWorkflowRuns": "", "failedInputFile": "", "failedOutputFile": "", "failedPrincipals": "", "failedSetExampleInput": "", "failedToFetchDesc": "", "failedToGetWorkflow": "", "failedUpdateExperimentAssignments": "", "failedUpdateTask": "", "failedUploadFile": "", "failedWorkflowIn": "", "failedWorkflowInputs": "", "fetchJEMTemplate": "", "fetchMutation": "", "fileContextDisabled": "", "fileFromURI": "", "fileTypeUnsupported": "", "forItem1": "", "forItem2": "", "generationFailed": "", "generationFailedMessage": "", "IDRequired": "", "inputNotFound": "", "invalidColumnReference": "", "invalidConfirm": "", "invalidConnection": "", "invalidEntryKey": "", "invalidSchemaReference": "", "invalidSystem": "", "invalidTable": "", "invalidTableReference": "", "issueDeletingInput": "", "itemNotFoundIndex": "", "noConvertedSQL": "", "noConvertedSQLMessage": "", "noData": "", "noDataExample": "", "noDataMessage": "", "noDataOrErrors": "", "noDataReturned": "", "noDataReturnedCEI": "", "noDataReturnedCTI": "", "noDataReturnedCWI": "", "noDataReturnedSEIFV": "", "noExampleOutputID": "", "noExamplePropsVOD": "", "noFileinDropdown": "", "noIDsprovided": "", "noMessageID": "", "noMsgIDDel": "", "noMsgIDEdit": "", "noMsgIDRegen": "", "noPrevTaskOutputs": "", "noRunID": "", "noStratAvailable": "", "noStrategyType": "", "noTaskDelete": "", "noTaskRevert": "", "noWorkflowID": "", "rejectedFiles": "", "schemaError": "", "somethingWentWrong": "", "taskDataUndefined": "", "taskExampleIDMissing": "", "taskIDinput": "", "taskIDoutput": "", "taskInputNotFound": "", "taskNotDefined": "", "taskNotFound": "", "transformingSystemName": "", "unableToGetDescription": "", "unexpectedFileError": "", "unexpectedInternalError": "", "unexpectedWorkflowInput": "", "unsupportedInputType": "", "unsupportedProvider": "", "unsupportedStrat": "", "workflowDataUndefined": "", "workflowDescNotFound": "", "workflowInputUri": ""}, "executeLLMGeneratedSQL": "", "failedToFetchDesc": "", "file": "", "fileContextDisabled": "", "fileType": "", "finalizedVersionDefault": "", "forWorkflowToRun": "", "GlobalInputs": {"addInput": "", "cancelOrSave": "", "dataInput": "", "newInput": ""}, "inactive": "", "inactiveExperiments": "", "includeSelectedRange": "", "incompleteInput": "", "inputType": "", "invalidSQLQuery": "", "loading": "", "makeActive": "", "messageDeleted": "", "messageRegenerated": "", "nameAgent": "", "nameAndDescription": "", "newItem": "", "newVersionName": "", "next": "", "noActiveExampleFound": "", "noActiveVersions": "", "noConnectionsAvailable": "", "noDataAvailable": "", "noEntityAvailable": "", "noExamplesFound": "", "noSourcesAvailable": "", "noSQLQuery": "", "noTablesAvailable": "", "notFoundIn": "", "notInUse": "", "notSupportPreview": "", "notYetRun": "", "noWorkflowRunsCan": "", "number": "", "open": "", "openInBuilder": "", "optionalDescription": "", "pCol": "", "permanentlyDelete1": "", "permanentlyDelete2": "", "permanentlyDeleteInput": "", "pleaseCheckQuery": "", "pleaseWaitStep": "", "pTable": "", "published": "", "reachToSupport": "", "readyToRun": "", "regenerateAssistantResponse": "", "regenerateMessage": "", "rename": "", "renameVersion": "", "requestedFormat": "", "runAgain": "", "runRemoved1": "", "runWorkflow": "", "save": "", "search": "", "searchConnections": "", "selectAgentType": "", "selectDate": "", "selectEntity": "", "selectFile": "", "selectOption": "", "selectSource": "", "selectStrat": "", "selectTable": "", "selectTool": "", "selectType": "", "selectVariant": "", "selectVersion": "", "setActive": "", "sqlEditor": "", "sqlEditorPrompt": "", "sqlEditorPromptLabel": "", "sqlEditorToggleLabel": "", "sqlGenerated": "", "sqlGeneratedSuccess": "", "statusChange1": "", "statusChange2": "", "statusChange3": "", "statusChange4": "", "statusChange5": "", "statusChange6": "", "statusChange7": "", "statusChange8": "", "stepOutput": "", "stepTaskName": "", "submit": "", "taskBeenRunSuccessfully": "", "taskDescription": "", "taskRunFailed": "", "taskRunSuccess": "", "testRun": "", "text": "", "textInput": "", "theRange": "", "thisVersionIsArchived": "", "thisVersionIsDraft": "", "thisVersionIsPublished": "", "title": "", "TODO": "", "treeViewTitle": "", "typeCommand": "", "unableCreateNewStep": "", "undo": "", "updatedTaskDescription": "", "uploadFile": "", "versionExecutedWorkflow": "", "versionOfStep": "", "versions": "", "versionsCanBeOTFS": "", "versionsTabSaved": "", "viewDetails": "", "viewRunHistory": "", "willRemoveAllStepsAfter": "", "workFlowRunTableSubtitle": ""}, "BuilderSectionTabs": {"InputForm": {"number": ""}}, "Connections": {"Actions": {"manage": "", "requestAccess": "", "setup": ""}, "Data": {"availableData": "", "noDataAvailable": ""}, "Errors": {"loadingPage": "", "pleaseTryAgain": ""}, "Header": {"searchPlaceholder": ""}, "Permissions": {"adminAccessRequired": ""}, "Status": {"connected": "", "notConnected": ""}, "title": ""}, "CreateAgent": {"advancedSetup": "", "experiments": "", "modes": "", "off": "", "on": ""}, "FileInput": {"dropToUpload": "", "formatList": "", "uploadFiles": ""}, "HeaderTitle": {"ariaLabels": {"cancelEditing": "", "editWorkflowName": "", "saveChanges": ""}, "errors": {"nameRequired": "", "nameTooLong": ""}, "toast": {"errorTitle": "", "invalidInputTitle": "", "updateAgentError": ""}, "tooltip": {"editName": ""}}, "Runner": {"activeAgentsRequire": "", "activityLog": "", "addInputsToRunAgent": "", "agentHasNoSteps": "", "agentHasNoSteps2": "", "agentPreview": "", "agentPreview2": "", "agentSteps": "", "allAgents": "", "approveRun": "", "downloadFile": "", "enterInput": "", "enterNumber": "", "enterText": "", "Errors": {"badNetworkResponse": "", "errorRunningWorkflow": "", "inputError": "", "noActiveExample": "", "noDataFromWorkflowRun": "", "noDataReturned": "", "noDataReturnedFromTaskRun": "", "noFileFoundForID": "", "noInputFoundForID": "", "noRunData": "", "noTaskDescription": "", "noWorkflowID": "", "requestError": ""}, "followingStepsExecuted": "", "generatingDescription": "", "inputsCount": "", "journalEntries": "", "makeActive": "", "noDataPreview": "", "rejectRun": "", "resumeRun": "", "runAgent": "", "runBy": "", "stepOfInput": "", "taskUnpublished": "", "unpublishedStep": "", "unsupportedInputType": "", "uploadFile": "", "uploadInput": "", "uploadInputFile": "", "viewErrorDetails": ""}, "SQLEditorSplit": {"LlmFeedback": {"additionalFeedbackOptional": "", "feedbackSubmitted": "", "feedbackSubmittedFailed": "", "feedbackSubmittedFailedMessage": "", "feedbackSubmittedSuccess": "", "rateResponseBad": "", "rateResponseGood": "", "submitFeedback": ""}, "viewLLMDescription": ""}}, "generics": {"actions": "", "active": "", "add": "", "addFlag": "", "archived": "", "authorize": "", "back": "", "beta": "", "build": "", "cancel": "", "canel": "", "chat": "", "close": "", "completed": "", "confirm": "", "continue": "", "count": "", "create": "", "created": "", "createdBy": "", "custom": "", "dashboard": "", "date": "", "delete": "", "deletion": "", "description": "", "details": "", "download": "", "downloadFile": "", "draft": "", "editor": "", "email": "", "error": "", "execute": "", "experiments": "", "failed": "", "file": "", "finish": "", "found": "", "generate": "", "ID": "", "inProgress": "", "inputs": "", "lastModified": "", "lastRun": "", "match": "", "message": "", "migrate": "", "name": "", "no": "", "output": "", "preview": "", "proceed": "", "publish": "", "regenerate": "", "rejected": "", "results": "", "role": "", "run": "", "runBy": "", "runDeletion": "", "save": "", "search": "", "select": "", "selected": "", "send": "", "settings": "", "startedAt": "", "status": "", "step": "", "strategy": "", "submit": "", "success": "", "task": "", "team": "", "teams": "", "templates": "", "text": "", "this": "", "title": "", "to": "", "total": "", "undo": "", "userStatus": "", "versions": "", "view": "", "you": ""}}