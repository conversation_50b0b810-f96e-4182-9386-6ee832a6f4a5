{"components": {"Connections": {"title": "Verbindungen transformieren", "Header": {"searchPlaceholder": "Verbindungen filtern"}, "Status": {"connected": "Verbunden"}, "Actions": {"setup": "Einrichten", "manage": "<PERSON><PERSON><PERSON><PERSON>", "requestAccess": "<PERSON><PERSON><PERSON>"}, "Permissions": {"adminAccessRequired": "<PERSON><PERSON><PERSON><PERSON>"}, "Errors": {"loadingPage": "Fehler beim Laden der Seite", "pleaseTryAgain": "Bitte versuchen Sie es erneut"}}, "HeaderTitle": {"errors": {"nameRequired": "Name darf nicht leer sein", "nameTooLong": "Name ist zu lang (maximal {{max}} <PERSON><PERSON><PERSON>)"}, "toast": {"errorTitle": "<PERSON><PERSON>", "updateAgentError": "Aktualisierung des Agentennamens fehlgeschlagen", "invalidInputTitle": "Ungültige Eingabe"}, "tooltip": {"editName": "Namen bearbeiten"}, "ariaLabels": {"editWorkflowName": "Workflow-<PERSON><PERSON> bear<PERSON>", "saveChanges": "Änderungen speichern", "cancelEditing": "Bearbeitung abbrechen"}}, "Runner": {"allAgents": "Alle Agenten"}, "Builder": {"Errors": {"noData": "<PERSON>ine Daten zurückgegeben", "invalidEntryKey": "Ungültiger Eintrag für Schlüssel {{key}}", "errorDuringCleanup": "Fehler während der Bereinigung", "couldNotFindTaskInput": "Konnte keine Aufgaben-Eingabe für Beispiel-Eingabe finden"}, "stepOutput": "Schritt {{index}} Ausgabe", "autoOpeningNewOutput": "Automatisches Öffnen neuer Ausgabe", "add": "Hinzufügen", "addFlag": "<PERSON><PERSON>"}, "AgentList": {"oneHumanStep": "<PERSON><PERSON><PERSON> diesen Agenten ist ein menschlicher Schritt erforderlich."}, "CreateAgent": {"modes": "<PERSON><PERSON>", "on": "Ein", "off": "Aus", "experiments": "Experimente"}, "FileInput": {"dropToUpload": "Oder Datei zum Hochladen hier ablegen", "formatList": "CSV, XLSX oder PDF kleiner als 10 MB", "uploadFiles": "<PERSON><PERSON>"}, "SQLEditorSplit": {"viewLLMDescription": "LLM-Beschreibung anzeigen", "LlmFeedback": {"additionalFeedbackOptional": "Zusätzliches Feedback (optional):", "feedbackSubmitted": "Feed<PERSON> <PERSON><PERSON><PERSON><PERSON>", "feedbackSubmittedFailed": "Feedback konnte nicht übermittelt werden", "feedbackSubmittedFailedMessage": "Bitte versuchen Sie es später noch einmal.", "feedbackSubmittedSuccess": "Vielen Dank für Ihr Feedback!", "rateResponseBad": "Bewerten Sie diese Antwort als schlecht", "rateResponseGood": "Bewerten Sie diese Antwort als gut", "submitFeedback": "Feed<PERSON> senden"}, "cancel": "Abbrechen", "chat": "Cha<PERSON>", "chatMSGTextArea": "Chat-Nachrichten-Textfeld", "checkSQLSyntax": "Bitte überprüfen Sie Ihre SQL-Syntax", "copySQL": "SQL kopieren", "copySQLErrorMessage": "Fehlermeldung kopieren", "copyUserPrompt": "Aufforderung kopieren", "editor": "Editor", "Errors": {"generationFailed": "Generierung fehlgeschlagen", "generationFailedMessage": "Wir konnten die SQL-Abfrage nicht erstellen. Bitte versuchen Sie es erneut.", "invalidConnection": "Ungültige Verbindungsvariable", "noConvertedSQL": "SQL-Konvertierung fehlgeschlagen", "noConvertedSQLMessage": "Wir konnten die SQL-Abfrage nicht umwandeln. Bitte versuchen Sie es erneut."}, "executeLLMGeneratedSQL": "SQL-Anweisung ausführen", "generate": "<PERSON><PERSON><PERSON>", "invalidSQLQuery": "Ungültige SQL-Abfrage", "pleaseCheckQuery": "Bitte überprüfen Sie Ihre Anfrage und versuchen Sie es erneut.", "sqlEditor": "SQL-Editor", "sqlGenerated": "SQL generiert", "sqlGeneratedSuccess": "SQL wurde erfolgreich generiert", "submit": "Senden", "taskBeenRunSuccessfully": "Ihre Aufgabe wurde erfolgreich ausgeführt.", "taskRunFailed": "Aufgabenausführung fehlgeschlagen", "taskRunSuccess": "Aufgabe erfolgreich ausgeführt", "typeCommand": "Be<PERSON><PERSON> e<PERSON>ben"}, "BuilderSectionTabs": {"InputForm": {"number": "<PERSON><PERSON><PERSON>"}, "build": "Build", "details": "Details", "inputs": "Eingaben", "versions": "<PERSON>en"}, "ActivityHistoryItem": {"runBy": "<PERSON><PERSON><PERSON> von"}, "ActivityLogSideDrawer": {"activityLog": "Aktivitätsprotokoll", "close": "Schließen"}, "AddInputModal": {"addInput": "Eingabe hinzufügen", "cancel": "Abbrechen", "save": "Speichern", "selectType": "Art auswählen"}, "AddStepButton": {"addNewStep": "Neuen Schritt hinzufügen", "unableCreateNewStep": "<PERSON><PERSON><PERSON> Schritt kann nicht erstellt werden"}, "AddTaskModal": {"addStep": "<PERSON><PERSON><PERSON>", "cancel": "Abbrechen", "editStep": "<PERSON><PERSON><PERSON>", "Errors": {"errorFetchingStrat": "Fehler beim Abrufen der Strategien", "noData": "<PERSON>ine Daten zurückgegeben", "noStratAvailable": "Keine Strategien verfügbar", "unsupportedStrat": "Nicht unterstützte Strategie"}, "loading": "Wird geladen ...", "name": "Name", "noDataAvailable": "<PERSON><PERSON> ve<PERSON>ü<PERSON>", "save": "Speichern", "selectStrat": "Strategie auswählen", "strategy": "Strategie"}, "AgentHistoryList": {"errorOccurredWhileLoadingRuns": "<PERSON>im <PERSON> der Durchläufe ist ein Fehler aufgetreten", "noRunsFound": "<PERSON><PERSON>ufe gefunden", "unexpectedError": "Ein unerwarteter Fehler ist aufgetreten"}, "AgentListPage": {"agents": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "searchAgents": "Suchagenten"}, "AgentsTable": {"connectedData": "Verknüpfte Daten", "description": "Beschreibung", "emptyState": "<PERSON>ine Agenten gefunden", "lastEdited": "Zuletzt bearbeitet", "lastRun": "<PERSON><PERSON><PERSON>", "name": "Name", "unexpectedFallthrough": "Unerwarteter Durchlauf beim Rendern der Agententabelle"}, "ApiKeysTab": {"APIKeyCreated": "API-Schlüssel erstellt", "apiKeyDeleted": "API-Schlüssel gelöscht", "apiKeys": "API-Schlüssel:", "APImakeRequests": "API-Anfragen stellen", "cancel": "Abbrechen", "close": "Schließen", "created": "<PERSON><PERSON><PERSON><PERSON>", "createdNewAPIKey": "Ein neuer API-Schlüssel wurde mit dem geheimen Schlüssel erstellt: {{secretKey}}", "createKey": "Schlüssel erstellen", "delete": "Löschen", "deleteAPIKey": "API-Schlüssel löschen", "lastUsed": "Zuletzt verwendet", "noAPIKey": "Kein API-Schlüssel", "permanentlyDeleteAPIKey": "API-Schlüssel dauerhaft löschen", "pleaseCopySecret": "Bitte kopieren Sie diesen geheimen Schlüssel jetzt. Sie werden ihn später nicht mehr sehen können!", "role": "<PERSON><PERSON>", "secretAPIKey": "Geheimer API-Schlüssel", "secretKey": "Geheimer Schlüssel", "somethingWentWrong": "Etwas ist schiefgelaufen", "startAPIKey": "API-Schlüssel starten", "unableCreateAPI": "API-Schlüssel konnte nicht erstellt werden", "unableDeleteAPI": "API-Schlü<PERSON> kann nicht gelöscht werden"}, "Audit": {"Errors": {"failedPrincipals": "Laden der Prinzipale fehlgeschlagen"}}, "BuilderLoadingPage": {"back": "Zurück", "createVersion": "Version erstellen", "downloadFile": "<PERSON><PERSON>", "noActiveExampleFound": "Kein aktives Beispiel gefunden", "noExamplesFound": "<PERSON><PERSON> gefunden", "noWorkflowID": "Keine Workflow-ID angegeben", "selectVersion": "Version auswählen"}, "BuilderPage": {"active": "Aktiv", "addNewStep": "Neuen Schritt hinzufügen", "analyzingFiles": "<PERSON><PERSON> werden analysiert...", "archived": "<PERSON><PERSON><PERSON><PERSON>", "downloadFile": "<PERSON><PERSON>", "draft": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "Errors": {"currentTaskNotFound": "Aktuelle Aufgabe nicht gefunden", "errorUpdatingTask": "Fehler beim Aktualisieren der Aufgabe", "fetchJEMTemplate": "Abrufen der JEM-Vorlage fehlgeschlagen", "fetchMutation": "Abrufen der Mutation fehlgeschlagen", "fileFromURI": "Datei konnte nicht von der URI abgerufen werden", "noIDsprovided": "Keine ID angegeben", "taskExampleIDMissing": "Beispielaufgaben-ID fehlt"}, "Hooks": {"Errors": {"errorCreatingContext": "Fehler beim Erstellen des Dateikontexts", "failedSetExampleInput": "Beispiel-Eingabe konnte nicht festgelegt werden", "failedUploadFile": "Datei-Upload fehlgeschlagen", "fetchJEMTemplate": "Abrufen der JEM-Vorlage fehlgeschlagen", "fileContextDisabled": "Dateikontext ist deaktiviert", "noDataReturnedCEI": "Keine Daten vom Erstellen des Beispiel-Inputs zurückgegeben", "noDataReturnedCTI": "Keine Daten vom Erstellen der Aufgaben-Eingabe zurückgegeben", "noDataReturnedCWI": "Keine Daten vom Erstellen des Workflow-Eingabewerts zurückgegeben", "noIDsprovided": "Keine ID angegeben", "noWorkflowID": "Keine Workflow-ID angegeben", "unsupportedInputType": "Nicht unterstützter Eingabetyp"}}, "pleaseWaitStep": "Bitte warten Si<PERSON>, bis der aktuelle Schritt abgeschlossen ist", "publish": "Veröffentlichen", "reachToSupport": "<PERSON>den <PERSON> sich an den Support", "readyToRun": "Bereit zum Starten", "stepTaskName": "Schritt: {{currentTask}}", "thisVersionIsArchived": "Diese Version ist archiviert", "thisVersionIsDraft": "Diese Version ist ein Entwurf", "thisVersionIsPublished": "Diese Version ist veröffentlicht", "viewDetails": "Details anzeigen"}, "BuilderPageHooks": {"task": "Aufgabe"}, "BuilderSectionActionBar": {"actionBarEdit": "<PERSON><PERSON><PERSON>", "actionBarSave": "Speichern", "createBlankVersion": "Eine leere Version erstellen", "deleteStep": "<PERSON><PERSON><PERSON> l<PERSON>", "deleteStepAfter": "<PERSON><PERSON> und alles danach löschen", "draftOneOutput": "Entwurf Eins Ausgabe", "rename": "Umbenennen", "save": "Speichern"}, "BuilderSectionHeader": {"Errors": {"taskNotFound": "Aufgabe nicht gefunden"}}, "BuilderV3API": {"Errors": {"badNetworkResponse": "Netzwerkantwort war nicht in Ordnung", "failedCreateExample": "Beispiel konnte nicht erstellt werden", "failedCreateInputs": "Eingaben konnten nicht erstellt werden", "failedCreateOutputs": "Ausgabe konnte nicht erstellt werden", "failedCreateTask": "Aufgabe konnte nicht erstellt werden", "failedCreateTaskInput": "Fehler beim Erstellen der Aufgaben-Eingabe", "failedCreateTaskOutput": "Fehler beim Erstellen der Ausgabe für die Aufgabe", "failedCreateWorkflow": "Fehler beim Erstellen des Workflows", "failedDeleteExample": "Beispiel konnte nicht gelöscht werden", "failedDeleteInputs": "Löschen der Eingaben fehlgeschlagen", "failedDeleteOutputs": "Ausgabe konnte nicht gelöscht werden", "failedExampleInputs": "Beispiel-Eingaben konnten nicht abgerufen werden", "failedExampleOutputs": "Beispielausgaben konnten nicht abgerufen werden", "failedGetExample": "Beispiel konnte nicht abgerufen werden", "failedGetTask": "Aufgabe konnte nicht abgerufen werden", "failedGetTaskInputURI": "Abrufen der Task-Eingabe-URI fehlgeschlagen", "failedGetTaskOutputs": "Fehler beim Abrufen der Aufgabenergebnisse", "failedInputFile": "Eingabedatei konnte nicht verarbeitet werden", "failedOutputFile": "Verarbeitung der Ausgabedatei fehlgeschlagen", "failedToGetWorkflow": "Workflow konnte nicht abgerufen werden", "failedUpdateTask": "Aktualisierung der Aufgabe fehlgeschlagen", "failedWorkflowIn": "Abrufen der Workflow-Eingabe fehlgeschlagen", "failedWorkflowInputs": "Abrufen der Workflow-Eingaben fehlgeschlagen", "noDataExample": "<PERSON><PERSON> Daten für das Beispiel verfügbar", "noDataMessage": "<PERSON><PERSON> Daten für die Nachricht verfügbar", "noPrevTaskOutputs": "Keine vorherigen Aufgabenergebnisse", "noRunID": "<PERSON><PERSON> ange<PERSON>ben", "noStrategyType": "<PERSON><PERSON> Strategietyp angegeben", "noTaskDelete": "<PERSON>ine Aufgabe zum Löschen vorhanden", "noTaskRevert": "<PERSON><PERSON> Aufgabe zum Rückgängig machen", "taskDataUndefined": "Aufgabendaten sind nicht definiert", "taskIDinput": "Aufgaben-ID für die Eingabe erforderlich", "taskIDoutput": "Aufgaben-ID für Ausgabe erforderlich", "workflowDataUndefined": "Workflow-Daten sind nicht definiert", "workflowInputUri": "Abrufen der Workflow-Eingabe-URI fehlgeschlagen"}}, "Chat": {"actions": "Aktionen", "areYouSureRegenerateResponse": "Sind <PERSON> sicher, dass Sie diese Nachricht neu generieren möchten? Das erneute Generieren dieser Antwort wird alle nachfolgenden Nachrichten im Thread löschen.", "cancel": "Abbrechen", "chatMSGTextArea": "Chat-Nachrichten-Textfeld", "confirm": "Bestätigen", "couldTakeAMinute": "Dies könnte eine Minute dauern", "delete": "Löschen", "deleteMessage": "Sind <PERSON> sicher, dass Sie diese Nachricht löschen möchten? Das Löschen dieser Antwort wird alle nachfolgenden Nachrichten in diesem Thread entfernen.", "editMessage": "Sind <PERSON> sicher, dass Sie diese Nachricht bearbeiten möchten? Das Bearbeiten dieser Antwort wird alle nachfolgenden Nachrichten im Thread löschen.", "enhanceWithAI": "Mit KI verbessern", "enhancingWithAI": "Verbessert durch KI", "Errors": {"emptyMessage": "<PERSON><PERSON><PERSON><PERSON> darf nicht leer sein", "invalidConfirm": "Ungültiger Bestätigungstyp", "noMsgIDDel": "<PERSON><PERSON>-ID zum Löschen vorhanden", "noMsgIDEdit": "<PERSON><PERSON>-<PERSON> zum Bearbeiten", "noMsgIDRegen": "<PERSON><PERSON>ch<PERSON>ten-ID für die Neuerstellung"}, "message": "Nachricht", "messageDeleted": "Nachricht gelöscht", "messageRegenerated": "Na<PERSON>richt neu generiert", "no": "<PERSON><PERSON>", "regenerate": "<PERSON><PERSON> gene<PERSON>", "regenerateMessage": "Nachricht neu generieren", "save": "Speichern", "send": "Senden", "submit": "Senden", "TODO": "ZU ERLEDIGEN", "typeCommand": "Be<PERSON><PERSON> e<PERSON>ben", "undo": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen"}, "ChatMessage": {"areYouSureRegenerateResponse": "Sind <PERSON> sicher, dass Sie die Antwort neu generieren möchten?", "deleteMessage": "Nachricht löschen", "draftMessage": "Nachrichtentwurf", "edit": "<PERSON><PERSON><PERSON>", "editMessage": "Nachricht bearbeiten", "Errors": {"noIDsprovided": "Keine ID angegeben", "noMessageID": "<PERSON><PERSON>-ID angegeben"}, "messageDeleted": "Nachricht gelöscht", "messageRegenerated": "Na<PERSON>richt neu generiert", "regenerateAssistantResponse": "Assistentenantwort neu generieren"}, "ChatWindow": {"chatWithAI": "Mit KI chatten", "Errors": {"appSyncAuthenticationValidationInvalid": "AppSync-Authentifizierungsüberprüfung ungültig", "noIDsprovided": "Keine ID angegeben", "rejectedFiles": "Einige Dateien wurden abgelehnt", "unexpectedInternalError": "<PERSON>er<PERSON><PERSON> interner <PERSON><PERSON>"}, "includeSelectedRange": "Ausgewählten Bereich einbeziehen", "theRange": "Der Bereich"}, "ConfirmMessageActionDialogue": {"aboutTo": "<PERSON><PERSON> sind da<PERSON>,", "aboutTo2": "Diese Aktion kann nicht rückgängig gemacht werden", "cancel": "Abbrechen", "confirmAction": "Aktion bestätigen", "Errors": {"noMessageID": "<PERSON><PERSON>-ID angegeben"}, "save": "Speichern"}, "ConfirmStepDeletionDialog": {"aboutToDeleteStep": "<PERSON><PERSON> sind dabei, einen <PERSON>hritt zu löschen", "aPreviousStep": "Ein vorheriger Schritt", "cancel": "Abbrechen", "deleteStep": "<PERSON><PERSON><PERSON> l<PERSON>", "Errors": {"noIDsprovided": "Keine ID angegeben"}, "willRemoveAllStepsAfter": "entfernt alle Schritte nach"}, "CreateWorkflowWizard": {"back": "Zurück", "cancel": "Abbrechen", "details": "Details", "nameAndDescription": "Name und Beschreibung", "next": "<PERSON><PERSON>", "selectAgentType": "Agententyp auswählen", "selectInput": "Eingabe auswählen"}, "DashboardPage": {"attemptedRuns": "Versuchte Durchläufe: {{totalAttemptedRuns}}", "completedAttempted": "der versuchten Durchläufe abgeschlossen", "completion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "completionRate": "Abschlussrate: {{completionRate}}%", "count": "<PERSON><PERSON><PERSON>", "createdAt": "Erstellt am", "dashboard": "Dashboard", "date": "Datum", "name": "Name", "runDetails": "Laufdetails", "runsByDate": "Läufe nach Datum", "runsByStatus": "Läufe nach Status", "runStats": "Laufstatistiken", "status": "Status", "strategy": "Strategie", "taskRunStats": "Aufgabenlauf-Statistiken", "taskRunStatsByDate": "Aufgabenlauf-Statistiken nach Datum", "taskRunStatsByStrat": "Aufgabenlauf-Statistiken nach Strategie", "total": "Gesamt", "totalRuns": "Gesamtläufe: {{totalRuns}}", "workflowID": "Workflow-ID", "workflowRunID": "Workflow-Ausführungs-ID", "workflowStats": "Workflow-Statistiken"}, "DeleteConfirmationDialog": {"actionPermanentDeleteTeam": "Diese Aktion wird das Team und alle zugehörigen Daten dauerhaft löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "cancel": "Abbrechen", "delete": "Löschen", "deleteTeam": "Team löschen"}, "DeleteInputMessageDialog": {"cancel": "Abbrechen", "deleteInput": "Eingabe löschen", "Errors": {"issueDeletingInput": "Problem beim Löschen der Eingabe"}, "permanentlyDelete1": "Diese Aktion wird die Eingabe dauerhaft löschen", "permanentlyDelete2": "Diese Aktion kann nicht rückgängig gemacht werden", "permanentlyDeleteInput": "Eingabe dauerhaft löschen"}, "DeleteWorkflowModal": {"areYouSure": "Sind <PERSON> sicher, dass Si<PERSON> dies löschen möchten", "cancel": "Abbrechen", "cannotUndo": "Diese Aktion kann nicht rückgängig gemacht werden", "confirm": "Bestätigen", "deletion": "Löschung"}, "DeleteWorkflowRunModal": {"allFilesUnchanged": "Alle Dateien bleiben unverändert.", "areYouSure": "Sind <PERSON> sicher, dass Si<PERSON> dies löschen möchten", "cancel": "Abbrechen", "cannotUndo": "Diese Aktion kann nicht rückgängig gemacht werden.", "confirm": "Bestätigen", "runDeletion": "Lauf-Löschung", "runRemoved1": "<PERSON> Lauf wird aus dem entfernt"}, "DetailsCard": {"addSection": "Abschnitt hinzufügen", "delete": "Löschen", "detailsHere": "Details hier", "Errors": {"forItem1": "<PERSON><PERSON><PERSON>", "forItem2": "an Index", "itemNotFoundIndex": "Element an Index nicht gefunden"}, "newItem": "Neues Element"}, "DetailsSlideout": {"cancel": "Abbrechen", "close": "Schließen", "details": "Details", "edit": "<PERSON><PERSON><PERSON>", "editTaskDescription": "Aufgabenbeschreibung bearbeiten", "error": "<PERSON><PERSON>", "Errors": {"somethingWentWrong": "Etwas ist schiefgelaufen!", "unableToGetDescription": "Wir konnten die Beschreibung nicht abrufen.", "workflowDescNotFound": "Beschreibung für Workflow-AufgabenbeispielSet nicht gefunden:"}, "failedToFetchDesc": "Fehler beim Abrufen der Aufgabenbeschreibung", "generatingDescription": "Beschreibung wird erstellt...", "regenerate": "<PERSON><PERSON> gene<PERSON>", "save": "Speichern", "success": "Aktion erfolgreich!", "taskDescription": "Aufgabenbeschreibung", "updatedTaskDescription": "Aktualisierte Aufgabenbeschreibung"}, "DetailsTab": {"cancel": "Abbrechen", "edit": "<PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON>", "Errors": {"somethingWentWrong": "Etwas ist schiefgelaufen!", "unableToGetDescription": "Wir konnten die Beschreibung nicht abrufen.", "workflowDescNotFound": "Beschreibung für Workflow-AufgabenbeispielSet nicht gefunden:"}, "failedToFetchDesc": "Aufgabenbeschreibung konnte nicht abgerufen werden", "regenerate": "<PERSON><PERSON> gene<PERSON>", "save": "Speichern", "success": "Aktion erfolgreich!", "updatedTaskDescription": "Aktualisierte Aufgabenbeschreibung"}, "EntityInputDropdown": {"selectOption": "Option auswählen"}, "ExternalJemLink": {"journalEntries": "Journalbuchungen"}, "FileDropdown": {"currentOutput": "Aktuelle Ausgabe", "Errors": {"noFileinDropdown": "<PERSON><PERSON> im Dropdown-<PERSON><PERSON>"}, "selectFile": "<PERSON>i ausw<PERSON>hlen"}, "FilePreview": {"downloadFile": "<PERSON><PERSON>", "Errors": {"unexpectedFileError": "<PERSON><PERSON><PERSON><PERSON>"}, "fileType": "Dateityp", "makeActive": "Aktivieren", "noDataPreview": "<PERSON><PERSON> Date<PERSON> zur Vorschau vorhanden.", "notSupportPreview": "Vorschau für diesen Dateityp nicht unterstützt"}, "GlobalInputs": {"addInput": "Eingabe hinzufügen", "cancelOrSave": "Abbrechen oder speichern, bevor neue Eingaben hinzugefügt werden", "dataInput": "<PERSON><PERSON><PERSON><PERSON>", "date": "Datum", "file": "<PERSON><PERSON>", "newInput": "Neue Eingabe", "text": "Text"}, "Header": {"active": "Aktiv", "activeAgentsRequire": "Aktive Agenten erfordern mindestens einen menschlichen Überprüfungsschritt.", "archived": "<PERSON><PERSON><PERSON><PERSON>", "draft": "<PERSON><PERSON><PERSON><PERSON>", "Errors": {"errorUpdatingWorkflow": "Fehler beim Aktualisieren des Workflow-Status"}, "testRun": "<PERSON><PERSON><PERSON>"}, "InputArgumentValue": {"addInput": "Eingabe hinzufügen", "Errors": {"unsupportedInputType": "Nicht unterstützter Eingabetyp"}, "selectDate": "Da<PERSON> ausw<PERSON>en", "textInput": "Texteingabe"}, "InputForm": {"builderInput": "Builder-Eingabe", "cancel": "Abbrechen", "date": "Datum", "delete": "Löschen", "description": "Beschreibung", "enterDescription": "Beschreibung eingeben", "enterTitle": "Einen Titel eingeben", "enterValue": "<PERSON><PERSON> e<PERSON>ben", "file": "<PERSON><PERSON>", "inputType": "Eingabetyp", "newInput": "Neue Eingabe", "requestedFormat": "Angefordertes Format", "save": "Speichern", "text": "Text", "title": "Titel"}, "InputMessage": {"addInput": "Eingabe hinzufügen", "cancel": "Abbrechen", "draftMessage": "Nachrichtentwurf", "Errors": {"noIDsprovided": "Keine ID angegeben"}, "save": "Speichern", "selectDate": "Da<PERSON> ausw<PERSON>en", "textInput": "Texteingabe"}, "Inputs": {"Errors": {"noIDsprovided": "Keine ID angegeben"}}, "InputsDropDown": {"inputsCount": "Eingaben ({{count}})"}, "InputsDropdown": {"addInputsPrevSteps": "Eingaben aus vorherigen Schritten hinzufügen", "Errors": {"inputNotFound": "Eingabe nicht gefunden", "noDataReturned": "<PERSON>ine Daten zurückgegeben", "noDataReturnedCEI": "Keine Daten vom Erstellen des Beispiel-Inputs zurückgegeben", "noDataReturnedCTI": "Keine Daten vom Erstellen der Aufgaben-Eingabe zurückgegeben", "noDataReturnedSEIFV": "<PERSON><PERSON> von SEIFV zurückgegeben", "noIDsprovided": "Keine ID angegeben", "taskNotFound": "Aufgabe nicht gefunden", "unexpectedWorkflowInput": "Unerwartete Workflow-Eingabe"}}, "InputSelectionForm": {"initialInputIntegrationDescription": "Mit Datenplattform verbinden", "initialInputIntegrationTitle": "Integration", "initialInputUploadFileDescription": "Excel, CSV usw. hochladen. Weitere Details hinzufügen", "initialInputUploadFileTitle": "<PERSON><PERSON> ho<PERSON>n", "selectInitialInput": "Anfängliche Eingabe auswählen", "thisWillEllipsis": "Dies wird..."}, "InputsTabSection": {"addInput": "Eingabe hinzufügen", "currentOutput": "Aktuelle Ausgabe", "edit": "<PERSON><PERSON><PERSON>", "Errors": {"failedCreateInputs": "Unerwarteter Datenfehler: Beispiel-Eingabe konnte nicht erstellt werden", "noDataOrErrors": "Unerwarteter Fehler: <PERSON><PERSON> oder Fehler zurückgegeben"}, "open": "<PERSON>en", "stepOutput": "Schritt {{index}} Ausgabe"}, "NavBar": {"backToApp": "Zurück zur App", "dashboard": "Dashboard", "teams": "Teams"}, "NewTaskPopover": {"createStep": "Neuen Schritt er<PERSON>llen", "description": "Beschreibung", "enterDesc": "Beschreibung eingeben", "enterTitle": "Einen Titel eingeben", "selectStrat": "Wählen Sie eine Strategie aus", "selectTool": "Auswahlwerkzeug", "submit": "Senden", "title": "Titel"}, "ReviewStep": {"approveRun": "<PERSON><PERSON>", "rejectRun": "<PERSON><PERSON>", "resumeRun": "<PERSON><PERSON>"}, "Row": {"deleteAgent": "Agent <PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "editAgent": "Agent bear<PERSON>ten", "oneHumanStep": "<PERSON><PERSON><PERSON> diesen Agenten ist ein menschlicher Schritt erforderlich.", "preview": "Vorschau", "run": "<PERSON><PERSON>", "showActivityLog": "Aktivitätsprotokoll anzeigen"}, "RunInputsWizard": {"addInputsToRunAgent": "Eingaben zum Ausführen des Agenten hinzufügen", "continue": "Fortfahren", "enterInput": "{{name}} e<PERSON>ben", "enterNumber": "<PERSON><PERSON> Z<PERSON>ben", "enterText": "Text eingeben", "finish": "<PERSON>den", "stepOfInput": "Schritt {{i}} von {{inputs}}", "unsupportedInputType": "Nicht unterstützter Eingabetyp", "uploadFile": "<PERSON><PERSON> ho<PERSON>n", "uploadInput": "{{name}} hochladen", "uploadInputFile": "{{name}}-<PERSON><PERSON>n"}, "RunnerHeader": {"activeAgentsRequire": "Aktive Agenten erfordern mindestens einen menschlichen Überprüfungsschritt.", "activityLog": "Aktivitätsprotokoll", "allAgents": "Alle Agenten", "runAgent": "Agent au<PERSON><PERSON><PERSON><PERSON>"}, "RunnerPage": {"agentHasNoSteps": "Der Agent hat derzeit keine Schritte.", "agentHasNoSteps2": "Bitte gehen Sie zum Builder, um Schritte für diesen Agenten hinzuzufügen und zu veröffentlichen.", "agentPreview": "Agentenvorschau", "agentPreview2": ": Überprüfen Sie die Schritte, bevor <PERSON> fortfahren.", "Errors": {"badNetworkResponse": "Netzwerkantwort war nicht in Ordnung", "inputError": "Unerwarteter Fehler: Erstellen der Workflow-Run-Eingabe fehlgeschlagen", "noDataFromWorkflowRun": "Unerwarteter Fehler: <PERSON><PERSON> wurden keine Daten vom erfolgreichen Erstellen des Workflow-Laufs empfangen", "noDataReturned": "<PERSON>ine Daten zurückgegeben", "noFileFoundForID": "<PERSON><PERSON> Datei für die Eingabe-ID gefunden: ", "noInputFoundForID": "<PERSON><PERSON> Eingabewert für die ID gefunden: ", "noRunData": "<PERSON><PERSON> empfangen", "noWorkflowID": "Keine Workflow-ID angegeben"}}, "ScriptsDropdown": {"selectStep": "<PERSON><PERSON><PERSON> au<PERSON>wählen"}, "ScriptsPage": {"Errors": {"failedToFetchScript": "Skript konnte nicht abgerufen werden", "failedToSaveScript": "Skript konnte nicht gespeichert werden"}, "scriptSaved": "Skript gespeichert", "workflow": "Workflow", "workflowScripts": "Workflow-Skripte"}, "SettingsSideDrawer": {"close": "Schließen", "save": "Speichern", "saving": "Speichern", "settings": "Einstellungen", "settingsError": "Fehler beim Speichern der Änderungen", "settingsSaved": "Änderungen erfolgreich gespeichert"}, "SourceTreeView": {"noConnectionsAvailable": "<PERSON><PERSON> Verbindungen verfügbar", "search": "<PERSON><PERSON>", "searchConnections": "Verbindungen suchen", "treeViewTitle": "<PERSON><PERSON><PERSON><PERSON>"}, "Spreadsheet": {"Errors": {"fileTypeUnsupported": "Dateityp nicht unterstützt", "unexpectedFileError": "<PERSON><PERSON><PERSON><PERSON>"}}, "SQLEditor": {"checkSQLSyntax": "SQL-Syntax überprüfen", "ensureInputsFilled": "<PERSON><PERSON><PERSON>, dass alle Eingabefelder ausgefüllt sind", "enterSQLQuery": "SQL-Abfrage eingeben", "Errors": {"badNetworkResponse": "Netzwerkantwort war nicht in Ordnung", "emptyPrompt": "<PERSON><PERSON>aufforderung", "enterSQLDescription": "SQL-Beschreibung eingeben", "errorExecutingFlolake": "<PERSON><PERSON> beim Ausführen von <PERSON>", "errorExecutingWorkflow": "Fehler beim Ausführen des Workflows", "errorHandlingInputs": "Fehlerbehandlung von Eingaben", "errorUpdatingTask": "Fehler beim Aktualisieren der Aufgabe", "generationFailed": "Generierung fehlgeschlagen", "generationFailedMessage": "Generierungsfehlermeldung", "noDataReturnedCEI": "Keine Daten vom Erstellen des Beispiel-Inputs zurückgegeben", "noDataReturnedCTI": "Keine Daten vom Erstellen der Aufgaben-Eingabe zurückgegeben", "noDataReturnedCWI": "Keine Daten vom Erstellen des Workflow-Eingabewerts zurückgegeben", "noExampleOutputID": "<PERSON><PERSON>-Ausgabe-ID", "noIDsprovided": "Keine ID angegeben", "schemaError": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "taskNotFound": "Aufgabe nicht gefunden"}, "execute": "Ausführen", "generate": "<PERSON><PERSON><PERSON>", "incompleteInput": "Unvollständige Eingabe", "invalidSQLQuery": "Ungültige SQL-Abfrage", "noSQLQuery": "Keine <PERSON>-Abfrage", "pleaseCheckQuery": "Bitte überprüfen Sie Ihre Anfrage", "select": "Art(en) der zugewiesenen Person auswählen", "sqlEditorPrompt": "Beschreiben Sie, welche Daten Sie abrufen möchten, z. B. \"Alle Kontodaten in Coupa abrufen\".", "sqlEditorPromptLabel": "SQL-Generierungsaufforderung", "sqlEditorToggleLabel": "KI-Modus", "sqlGenerated": "SQL generiert", "sqlGeneratedSuccess": "SQL erfolgreich generiert", "taskBeenRunSuccessfully": "Aufgabe wurde erfolgreich ausgeführt", "taskRunFailed": "Aufgabenausführung fehlgeschlagen", "taskRunSuccess": "Aufgabe erfolgreich ausgeführt"}, "StatusChangeDialog": {"activateOnly": "Nur aktivieren", "activateTest": "Test aktivieren", "cancel": "Abbrechen", "changingFromTo": "<PERSON><PERSON><PERSON> <PERSON> {{fromStatus}} zu {{toStatus}}", "delete": "Löschen", "deleteVersion": "Version löschen", "deleteVersionBody": "<PERSON><PERSON><PERSON> wird diese Version dauerhaft gelöscht. Diese Aktion kann nicht rückgängig gemacht werden.", "Errors": {"noIDsprovided": "Kein workflowId, taskId oder exampleSetId in den Parametern angegeben", "taskNotDefined": "Aufgabe nicht definiert"}, "statusChange1": "<PERSON>n Sie diese Version in einen Entwurf zurücksetzen, ble<PERSON>t keine Version aktiv. Auf diesem Agenten können keine Ausführungen gestartet werden, bis eine aktive Version ausgewählt wurde.", "statusChange2": "<PERSON>n Sie diese Version aktivieren, werden alle anderen aktiven Versionen deaktiviert. Ausführungen werden mit dieser Version durchgeführt.", "statusChange3": "Durch diese Aktion wird diese Version aus dem Archiv geholt und aktiviert. Alle anderen aktiven Versionen werden deaktiviert. Ausführungen erfolgen mit dieser Version.", "statusChange4": "<PERSON><PERSON><PERSON> wird diese Version in einen Entwurf umgewandelt. Läufe verwenden diese Version nur, wenn sie als „aktiv“ ausgewählt ist.", "statusChange5": "Diese Aktion wird diese Version archivieren. Archivierte Versionen können jederzeit wiederhergestellt werden.", "statusChange6": "Diese Aktion wird diese Version archivieren. Archivierte Versionen können jederzeit wiederhergestellt werden. Auf diesem Agenten können keine Ausführungen gestartet werden, bis eine aktive Version ausgewählt wurde.", "statusChange7": "Diese Aktion wird diese Version deaktivieren. Läufe werden mit dieser Version nicht durchgeführt.", "statusChange8": "<PERSON>n Sie diese Version aktivieren, wird sie als Standard für zukünftige Agentenläufe festgelegt. Wir <PERSON><PERSON><PERSON><PERSON>, einen <PERSON>lau<PERSON>f<PERSON>, um sicherzustellen, dass jeder Schritt funktioniert, bevor <PERSON> fort<PERSON>hren."}, "Step": {"Errors": {"noDataReturnedFromTaskRun": "Unerwarteter Fehler: <PERSON><PERSON> Daten aus dem Aufgabenprotokoll zurückgegeben"}, "viewErrorDetails": "Fehlerdetails anzeigen"}, "StepList": {"agentSteps": "Agentenschritte", "Errors": {"badNetworkResponse": "Netzwerkantwort war nicht in Ordnung", "errorRunningWorkflow": "Fehler beim Ausführen des Workflows:", "requestError": "Anforderungsfehler"}, "followingStepsExecuted": "Die folgenden Schritte werden ausgeführt, wenn ein Agent gestartet wird."}, "StepResults": {"results": "Ergebnisse", "runBy": "<PERSON><PERSON><PERSON> von"}, "Steps": {"delete": "Löschen", "Errors": {"noIDsprovided": "Keine ID angegeben"}, "view": "<PERSON><PERSON><PERSON>"}, "StepsSidebarList": {"add": "Hinzufügen", "addStep": "<PERSON><PERSON><PERSON>", "blockType": "Blocktyp", "inputs": "Eingaben"}, "TeamDropdownSelect": {"selectTeamMigrate": "Team zum Migrieren auswählen"}, "TeamMigrationModal": {"cancel": "Abbrechen", "migrate": "<PERSON><PERSON><PERSON><PERSON>", "newCreatedBy": "<PERSON><PERSON> von", "newEntityID": "Neue Entitäts-ID", "selectTeamMigrate": "Team zum Migrieren auswählen"}, "TeamModal": {"cancel": "Abbrechen", "save": "Speichern", "teamExternalID": "Team-Externe-ID", "teamName": "Teamname"}, "TeamPage": {"additionalFunctionality": "Zusätzliche Funktionalität", "APISwaggerUI": "API Swagger-Benutzeroberfläche", "notFound": "Nicht gefunden", "team": "Team"}, "UseCaseForm": {"addCustomPrompt": "Fügen Sie dem Schritt eine benutzerdefinierte Eingabeaufforderung hinzu. Dadurch wird die Standardaufforderung für diesen Schritt überschrieben.", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templates": "Vorlagen"}, "UserPage": {"actions": "Aktionen", "actionWillMoveUser": "Diese Aktion wird den Benutzer verschieben", "actionWillMoveYou": "Diese Aktion wird Sie weiterleiten", "cancel": "Abbrechen", "changeTeam": "Team wechseln", "changeTeamAndLogOut": "Team wechseln und abmelden", "deleteUser": "Benutzer löschen", "emailedTempPassword": "Per E-Mail gesendetes temporäres Passwort", "emailSent": "E-Mail gesendet", "notFound": "Nicht gefunden", "permanentlyDelete": "Endgültig löschen", "permDeleteUser": "Benutzer dauerhaft löschen", "resendEmail": "E-Mail erneut senden", "roleColon": "Rolle: ", "roleUpdated": "Rolle aktualisiert", "somethingWentWrong": "Etwas ist schiefgelaufen", "switchTeams": "Team wechseln", "team": "Team", "teamColon": "Team:", "typeDelete": "<PERSON><PERSON>: ", "unableDeleteUser": "<PERSON><PERSON><PERSON> kann nicht gelöscht werden", "unableTempPassword": "Temporäres Passwort kann nicht erstellt werden", "unableToUpdateUserRole": "Benutzerrolle kann nicht aktualisiert werden", "userDeleted": "<PERSON>utz<PERSON> gelöscht", "userDeletedMessage": "Benutzer erfolgreich <PERSON>", "userDeleteUndone": "Benutzerlöschung rückgängig gemacht", "userRoleUpdated": "Benutzerrolle aktualisiert", "userStatus": "Status: {{status}}", "you": "<PERSON><PERSON><PERSON>"}, "UsersTab": {"email": "E-Mail", "getStartedInviting": "Einladen starten", "inviteByEmail": "Per E-Mail einladen", "inviteMoreUsers": "<PERSON><PERSON><PERSON> e<PERSON>n", "inviteSent": "Einladung gesendet", "inviteUser": "<PERSON><PERSON><PERSON> e<PERSON>n", "noOneHere": "<PERSON><PERSON><PERSON> hier", "role": "<PERSON><PERSON>", "sentInvitationTo": "Einladung gesendet an", "somethingWentWrong": "Etwas ist schiefgelaufen", "status": "Status", "unableToInviteUser": "<PERSON><PERSON><PERSON> kann nicht eingeladen werden", "users": "<PERSON><PERSON><PERSON>", "you": "<PERSON><PERSON><PERSON>"}, "VersionCard": {"active": "Aktiv", "cancel": "Abbrechen", "confirm": "Bestätigen", "delete": "Löschen", "editTitle": "Titel bearbeiten", "Errors": {"noIDsprovided": "Kein workflowId, taskId oder exampleSetId in den Parametern für die Builder-Seite angegeben"}, "newVersionName": "Neuer Versionsname", "renameVersion": "Version umbenennen", "setActive": "Als aktiv festlegen", "view": "<PERSON><PERSON><PERSON>"}, "VersionDropdown": {"Errors": {"noIDsprovided": "Keine ID angegeben"}}, "VersionOptionsDropdown": {"deleteVersion": "Version löschen", "Errors": {"noExamplePropsVOD": "<PERSON><PERSON>-Props für das Dropdown-Menü der Versionsoptionen"}, "newVersionName": "Neuer Versionsname", "renameVersion": "Version umbenennen"}, "VersionSlideout": {"archived": "<PERSON><PERSON><PERSON><PERSON>", "default": "Standardeinstellung", "developmentVer": "Entwicklungsversion", "draft": "<PERSON><PERSON><PERSON><PERSON>", "Errors": {"noIDsprovided": "Keine ID angegeben"}, "finalizedVersionDefault": "Finalisierte Standardversion", "forWorkflowToRun": "Damit der Workflow ausgeführt werden kann", "noActiveVersions": "Keine aktiven Versionen", "notInUse": "Nicht in Gebrauch", "noWorkflowRunsCan": "Es können keine Workflow-Ausführungen durchgeführt werden", "published": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "select": "Art(en) der zugewiesenen Person auswählen", "selected": "Ausgewählt", "versionExecutedWorkflow": "Version des ausgeführten Workflows", "versionOfStep": "Version des Schritts", "versions": "<PERSON>en", "versionsCanBeOTFS": "Versionen können spontan ausgewählt werden"}, "VersionsTab": {"active": "Aktiv", "draft": "<PERSON><PERSON><PERSON><PERSON>", "versionsTabSaved": "Gespe<PERSON>rt"}, "WorkflowForm": {"advancedSetup": "Erweiterte Einrichtung", "agentName": "Agentenname", "description": "Beschreibung", "entity": "Gesellschaft", "Errors": {"agentNameRequired": "Agentenname ist erforderlich", "errorFetchingEntities": "Fehler beim Abrufen der Einträge"}, "loading": "Wird geladen ...", "nameAgent": "Geben Sie Ihrem Agenten einen Namen", "noDataAvailable": "<PERSON><PERSON> ve<PERSON>ü<PERSON>", "noEntityAvailable": "<PERSON>ine Entität verfügbar", "optionalDescription": "Optionale Beschreibung", "selectEntity": "Einheit auswählen"}, "WorkflowModals": {"agentDescriptionOptional": "Agentenbeschreibung (optional)", "agentNamePlaceholder": "Platzhalter für Agentennamen", "cancel": "Abbrechen", "createNewWorkflow": "Neuen Workflow erstellen", "editAgent": "Agent bear<PERSON>ten", "save": "Speichern"}, "WorkflowRunNotes": {"agentDescriptionOptional": "Agentenbeschreibung (optional)", "cancel": "Abbrechen", "save": "Speichern"}, "WorkflowRuns": {"completed": "Abgeschlossen", "failed": "Fehlgeschlagen", "inProgress": "In Bearbeitung", "rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "WorkflowRunTable": {"deleteRun": "<PERSON><PERSON>", "editNotes": "Notizen bearbeiten", "name": "Name", "notYetRun": "wurde noch nicht ausgeführt", "runAgain": "<PERSON>rneut ausführen", "runBy": "<PERSON><PERSON><PERSON> von", "startedAt": "Begonnen am", "status": "Status", "this": "<PERSON>s", "view": "<PERSON><PERSON><PERSON>", "viewRunHistory": "Ausführungsverlauf anzeigen", "workFlowRunTableSubtitle": "Führen Sie diesen Workflow aus, um die Ergebnisse zu sehen"}, "WorkflowsTab": {"createdAt": "Erstellt am", "filterWorkflows": "Workflows filtern", "hasBeenMigrated": "wurde migriert", "ID": "ID", "loadMore": "<PERSON><PERSON> <PERSON>", "migrate": "<PERSON><PERSON><PERSON><PERSON>", "name": "Name", "noWorkflowsFound": "Keine Workflows gefunden", "somethingWentWrong": "Etwas ist schiefgelaufen", "theWorkflow": "Der Arbeitsablauf", "unableToMigrateWorkflow": "Workflow konnte nicht migriert werden", "viewScripts": "Skripte anzeigen", "workflowMigrated": "Workflow migriert", "workflows": "Workflows"}, "WorkflowTable": {"build": "<PERSON><PERSON><PERSON><PERSON>", "buildWorkflow": "Build-Workflow", "createdBy": "<PERSON><PERSON><PERSON><PERSON> von", "delete": "Löschen", "deleteWorkflow": "Workflow löschen", "description": "Beschreibung", "edit": "<PERSON><PERSON><PERSON>", "editWorkflow": "Workflow bearbeiten", "lastModified": "Zuletzt geändert", "lastRun": "<PERSON><PERSON><PERSON>", "run": "<PERSON><PERSON>", "runWorkflow": "Workflow ausführen", "status": "Status"}}}