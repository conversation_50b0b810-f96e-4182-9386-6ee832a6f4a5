import * as apiSdk from "@floqastinc/transform-v3";
import { fetchStuff, getLambdaEndpoint } from "@/utils/request";

const params: [apiSdk.FetchLike, apiSdk.TransformOptions | undefined] = [
  fetchStuff,
  {
    root: `${getLambdaEndpoint("transform_api", true)}/v3`,
  },
];
new apiSdk.HttpTaskDescriptionService(...params);

export const v3 = {
  appsyncAuthentication: new apiSdk.HttpAppsyncAuthenticationService(...params),
  workflows: new apiSdk.HttpWorkflowService(...params),
  workflowInputs: new apiSdk.HttpWorkflowInputService(...params),
  workflowOutputs: new apiSdk.HttpWorkflowOutputService(...params),
  tasks: new apiSdk.HttpTaskService(...params),
  taskInputs: new apiSdk.HttpTaskInputService(...params),
  taskOutputs: new apiSdk.HttpTaskOutputService(...params),
  taskDescriptions: new apiSdk.HttpTaskDescriptionService(...params),
  examples: new apiSdk.HttpExampleService(...params),
  exampleInputs: new apiSdk.HttpExampleInputService(...params),
  exampleOutputs: new apiSdk.HttpExampleOutputService(...params),
  experiments: new apiSdk.HttpExperimentService(...params),
  experimentAssignments: new apiSdk.HttpExperimentAssignmentService(...params),
  messages: new apiSdk.HttpMessageService(...params),
  runs: new apiSdk.HttpRunService(...params),
  fileContext: new apiSdk.HttpFileContextService(...params),
  fileSamples: new apiSdk.HttpFileSampleService(...params),
  entities: new apiSdk.HttpEntityService(...params),
  strategies: new apiSdk.HttpStrategyService(...params),
  jemTemplate: new apiSdk.HttpJemTemplateService(...params),
  flolakeExecuteService: new apiSdk.HttpFlolakeExecuteService(...params),
  llmMetricsLogsService: new apiSdk.HttpLlmMetricsLogService(...params),
  nlToSqlService: new apiSdk.HttpNlToSqlService(...params),
  settings: new apiSdk.HttpSettingService(...params),
  prompts: new apiSdk.HttpPromptService(...params),
};

// TODO: Move this somewhere else more unified/util ish
export class ApiError extends Error {
  constructor(readonly errors: apiSdk.Error[]) {
    super(errors.map((e) => e.title).join(", "));

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ApiError);
    }
  }
}

export default v3;
